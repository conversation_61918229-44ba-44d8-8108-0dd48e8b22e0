/**
 * PostgreSQL部门数据访问层
 * 替换SQLite的departmentRepository，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('./basePostgresRepository');
const logger = require('../utils/logger');

class PostgresDepartmentRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL部门数据访问层初始化完成');
        }
    }

    /**
     * 获取所有部门
     */
    async findAll() {
        try {
            const departments = await this.findMany(`
                SELECT * FROM departments 
                ORDER BY name ASC
            `);
            return departments.map(dept => this.transformDepartment(dept));
        } catch (error) {
            logger.error('获取部门列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找部门
     */
    async findById(id) {
        try {
            const department = await this.findOne('SELECT * FROM departments WHERE id = $1', [id]);
            return department ? this.transformDepartment(department) : null;
        } catch (error) {
            logger.error(`根据ID查找部门失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据名称查找部门
     */
    async findByName(name) {
        try {
            const department = await this.findOne('SELECT * FROM departments WHERE name = $1', [name]);
            return department ? this.transformDepartment(department) : null;
        } catch (error) {
            logger.error(`根据名称查找部门失败 (${name}):`, error);
            throw error;
        }
    }

    /**
     * 创建新部门
     */
    async create(departmentData) {
        try {
            const now = new Date().toISOString();
            const id = departmentData.id || this.generateId();

            const result = await this.query(`
                INSERT INTO departments (
                    id, name, description, active, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6)
                RETURNING *
            `, [
                id,
                departmentData.name,
                departmentData.description || '',
                true, // 默认设置为活跃状态
                now,
                now
            ]);

            return this.transformDepartment(result.rows[0]);
        } catch (error) {
            logger.error('创建部门失败:', error);
            throw error;
        }
    }

    /**
     * 更新部门
     */
    async update(id, departmentData) {
        try {
            const now = new Date().toISOString();

            const result = await this.query(`
                UPDATE departments SET
                    name = $1, description = $2, active = $3, updated_at = $4
                WHERE id = $5
                RETURNING *
            `, [
                departmentData.name,
                departmentData.description || '',
                departmentData.active !== false,
                now,
                id
            ]);

            return result.rows.length > 0 ? this.transformDepartment(result.rows[0]) : null;
        } catch (error) {
            logger.error(`更新部门失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除部门
     */
    async delete(id) {
        try {
            const result = await this.query('DELETE FROM departments WHERE id = $1', [id]);
            return result.rowCount > 0;
        } catch (error) {
            logger.error(`删除部门失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 检查部门名称是否已存在
     */
    async checkNameExists(name, excludeId = null) {
        try {
            let query = 'SELECT COUNT(*) as count FROM departments WHERE name = $1';
            let params = [name];
            
            if (excludeId) {
                query += ' AND id != $2';
                params.push(excludeId);
            }

            const result = await this.findOne(query, params);
            return result ? parseInt(result.count) > 0 : false;
        } catch (error) {
            logger.error(`检查部门名称是否存在失败 (${name}):`, error);
            throw error;
        }
    }

    /**
     * 检查部门中是否有用户
     */
    async checkUsersInDepartment(departmentName) {
        try {
            const result = await this.findOne(`
                SELECT COUNT(*) as count 
                FROM users 
                WHERE department = $1 AND active = true
            `, [departmentName]);
            return result ? parseInt(result.count) > 0 : false;
        } catch (error) {
            logger.error(`检查部门用户失败 (${departmentName}):`, error);
            throw error;
        }
    }

    /**
     * 获取部门统计信息
     */
    async getDepartmentStats() {
        try {
            const stats = await this.findMany(`
                SELECT 
                    d.id,
                    d.name,
                    d.description,
                    d.active,
                    COUNT(u.id) as user_count
                FROM departments d
                LEFT JOIN users u ON d.name = u.department AND u.active = true
                GROUP BY d.id, d.name, d.description, d.active
                ORDER BY d.name ASC
            `);
            
            return stats.map(stat => ({
                ...this.transformDepartment(stat),
                userCount: parseInt(stat.user_count) || 0
            }));
        } catch (error) {
            logger.error('获取部门统计信息失败:', error);
            throw error;
        }
    }

    /**
     * 获取活跃部门列表
     */
    async findActiveDepartments() {
        try {
            const departments = await this.findMany(`
                SELECT * FROM departments 
                WHERE active = true 
                ORDER BY name ASC
            `);
            return departments.map(dept => this.transformDepartment(dept));
        } catch (error) {
            logger.error('获取活跃部门列表失败:', error);
            throw error;
        }
    }

    /**
     * 批量更新部门状态
     */
    async updateDepartmentStatus(ids, active) {
        try {
            const now = new Date().toISOString();
            const placeholders = ids.map((_, index) => `$${index + 1}`).join(',');
            
            const result = await this.query(`
                UPDATE departments 
                SET active = $${ids.length + 1}, updated_at = $${ids.length + 2}
                WHERE id IN (${placeholders})
            `, [...ids, active, now]);

            return result.rowCount;
        } catch (error) {
            logger.error('批量更新部门状态失败:', error);
            throw error;
        }
    }

    /**
     * 转换部门数据格式
     */
    transformDepartment(department) {
        if (!department) return null;
        
        return {
            ...department,
            active: Boolean(department.active)
        };
    }

    /**
     * 初始化默认部门数据
     */
    async initDefaultDepartments() {
        try {
            const defaultDepartments = [
                { name: '管理部', description: '负责公司整体管理和决策' },
                { name: '生产部', description: '负责产品生产和制造' },
                { name: '质量部', description: '负责产品质量控制和检测' },
                { name: '技术部', description: '负责技术研发和创新' },
                { name: '销售部', description: '负责产品销售和市场推广' }
            ];

            for (const dept of defaultDepartments) {
                const exists = await this.checkNameExists(dept.name);
                if (!exists) {
                    await this.create(dept);
                }
            }
        } catch (error) {
            logger.error('初始化默认部门数据失败:', error);
            throw error;
        }
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString() + Math.random().toString(36).substr(2, 9);
    }
}

module.exports = PostgresDepartmentRepository;
