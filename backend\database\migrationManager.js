/**
 * 数据库迁移管理器
 * 负责执行数据库结构迁移
 */

const logger = require('../utils/logger');
const EquipmentHealthTableMigration = require('./migrations/001_update_equipment_health_table');
const WarehouseTablesMigration = require('./migrations/002_create_warehouse_tables');
const UserTrackingTablesMigration = require('./migrations/003_create_user_tracking_tables');

class MigrationManager {
    constructor(database) {
        this.db = database;
        this.migrations = [
            {
                id: '001_update_equipment_health_table',
                name: '更新设备健康度表结构',
                migration: EquipmentHealthTableMigration
            },
            {
                id: '002_create_warehouse_tables',
                name: '创建仓库管理系统数据表',
                migration: WarehouseTablesMigration
            },
            {
                id: '003_create_user_tracking_tables',
                name: '创建用户跟踪相关表',
                migration: UserTrackingTablesMigration
            }
        ];
        
        this.initMigrationTable();
    }

    /**
     * 初始化迁移记录表
     */
    initMigrationTable() {
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS migrations (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                executed_at TEXT NOT NULL,
                success INTEGER NOT NULL DEFAULT 1
            )
        `);
    }

    /**
     * 检查迁移是否已执行
     */
    async isMigrationExecuted(migrationId) {
        const result = await this.db.query('SELECT id FROM migrations WHERE id = $1 AND success = true', [migrationId]);
        return result.rows.length > 0;
    }

    /**
     * 记录迁移执行结果
     */
    async recordMigration(migrationId, name, success = true) {
        await this.db.query(`
            INSERT INTO migrations (id, name, executed_at, success)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                executed_at = EXCLUDED.executed_at,
                success = EXCLUDED.success
        `, [migrationId, name, new Date().toISOString(), success]);
    }

    /**
     * 执行所有待执行的迁移
     */
    async runMigrations() {
        // 只在详细日志模式下显示迁移检查信息
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('开始检查数据库迁移...');
        }

        let executedCount = 0;

        for (const migrationConfig of this.migrations) {
            const { id, name, migration: MigrationClass } = migrationConfig;

            if (this.isMigrationExecuted(id)) {
                logger.debug(`迁移 ${id} 已执行，跳过`);
                continue;
            }

            try {
                if (process.env.VERBOSE_LOGS === 'true') {
                    logger.info(`执行迁移: ${name} (${id})`);
                }

                const migration = new MigrationClass(this.db);
                await migration.migrate();

                this.recordMigration(id, name, true);
                executedCount++;

                if (process.env.VERBOSE_LOGS === 'true') {
                    logger.info(`迁移 ${id} 执行成功`);
                }

            } catch (error) {
                logger.error(`迁移 ${id} 执行失败:`, error);
                this.recordMigration(id, name, false);
                throw error;
            }
        }

        if (executedCount > 0) {
            if (process.env.VERBOSE_LOGS === 'true') {
                logger.info(`数据库迁移完成，共执行 ${executedCount} 个迁移`);
            }
        } else {
            // 只在详细日志模式下显示无需迁移信息
            if (process.env.VERBOSE_LOGS === 'true') {
                logger.info('数据库已是最新版本，无需迁移');
            }
        }
    }

    /**
     * 获取迁移历史
     */
    async getMigrationHistory() {
        const result = await this.db.query('SELECT * FROM migrations ORDER BY executed_at DESC');
        return result.rows;
    }
}

module.exports = MigrationManager;
