{"name": "makrite-application-system-backend", "version": "1.0.0", "description": "管理系统后端", "main": "server.js", "scripts": {"start": "node server.js", "dev": "cross-env NODE_ENV=development nodemon server.js"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "cross-env": "^10.0.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.1", "pdf-lib": "^1.17.1", "sharp": "^0.34.2", "sqlite3": "^5.1.7", "uuid": "^9.0.0", "validator": "^13.11.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"nodemon": "^2.0.22"}}