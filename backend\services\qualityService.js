/**
 * 质量管理服务
 * 处理检测报告相关的业务逻辑
 */

const { databaseAdapter } = require('../database/databaseAdapter');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const fs = require('fs');
const config = require('../config');
const logger = require('../utils/logger');
const { fixFilenameEncoding } = require('../utils/encodingUtils');

class QualityService {
    constructor() {
        this.repository = databaseAdapter.getQualityRepository();
    }

    /**
     * 生成报告编号
     * 格式：QR + YYYYMMDD + 4位序号
     * 编号严格递增，不重复使用已删除的编号
     */
    generateReportNumber() {
        const today = new Date();
        const dateStr = today.getFullYear().toString() +
                       (today.getMonth() + 1).toString().padStart(2, '0') +
                       today.getDate().toString().padStart(2, '0');

        // 获取当天已使用的最大序号（包括已删除的记录）
        const maxSequence = this.repository.getMaxUsedSequenceForDate(`QR${dateStr}`);
        const nextSequence = (maxSequence + 1).toString().padStart(4, '0');

        return `QR${dateStr}${nextSequence}`;
    }

    /**
     * 创建检测报告
     * @param {Object} reportData - 报告数据
     * @param {Array} files - 上传的文件列表
     * @param {string} userId - 用户ID
     * @returns {Object} 创建的报告信息
     */
    createReport(reportData, files = [], userId) {
        return this.repository.transaction(() => {
            try {
                const reportId = uuidv4();
                const reportNumber = this.generateReportNumber();
                const now = new Date().toISOString();

                // 创建报告数据
                const reportRecord = {
                    id: reportId,
                    report_number: reportNumber,
                    title: reportData.title,
                    description: reportData.description || '',
                    test_type: reportData.testType,
                    test_date: reportData.testDate,
                    sample_info: reportData.sampleInfo || '',
                    test_method: reportData.testMethod || '',
                    test_standard: reportData.testStandard || '',
                    test_result: reportData.testResult || '',
                    conclusion: reportData.conclusion || '',
                    uploaded_by: userId,
                    uploaded_at: now,
                    status: reportData.status || 'published',
                    created_at: now,
                    updated_at: now
                };

                // 记录已使用的报告编号
                this.repository.recordUsedReportNumber(reportNumber);

                // 插入报告基本信息
                this.repository.createReport(reportRecord);

                // 插入文件信息
                if (files && files.length > 0) {
                    files.forEach(file => {
                        const fileId = uuidv4();
                        const fileExtension = path.extname(file.originalname).toLowerCase();

                        // 修复文件名编码
                        const originalFilename = fixFilenameEncoding(file.originalname);

                        const fileRecord = {
                            id: fileId,
                            report_id: reportId,
                            original_filename: originalFilename,
                            stored_filename: file.filename,
                            file_path: file.path,
                            file_size: file.size,
                            file_type: fileExtension,
                            mime_type: file.mimetype,
                            uploaded_at: now
                        };

                        this.repository.createFile(fileRecord);
                    });
                }

                return this.getReportById(reportId);
            } catch (error) {
                logger.error('创建检测报告失败:', error);
                throw error;
            }
        });
    }

    /**
     * 获取检测报告列表
     * @param {Object} options - 查询选项
     * @returns {Object} 报告列表和分页信息
     */
    getReports(options = {}) {
        try {
            const {
                page = 1,
                limit = 10,
                testType,
                status,
                uploadedBy,
                startDate,
                endDate,
                search
            } = options;

            // 获取所有报告
            let reports = this.repository.findAllReports();

            // 应用筛选条件
            if (testType) {
                reports = reports.filter(report => report.test_type === testType);
            }

            if (status) {
                reports = reports.filter(report => report.status === status);
            }

            if (uploadedBy) {
                reports = reports.filter(report => report.uploaded_by === uploadedBy);
            }

            if (startDate) {
                reports = reports.filter(report => report.test_date >= startDate);
            }

            if (endDate) {
                reports = reports.filter(report => report.test_date <= endDate);
            }

            if (search) {
                const searchLower = search.toLowerCase();
                reports = reports.filter(report =>
                    report.title.toLowerCase().includes(searchLower) ||
                    report.description.toLowerCase().includes(searchLower) ||
                    report.report_number.toLowerCase().includes(searchLower)
                );
            }

            const total = reports.length;
            const offset = (page - 1) * limit;
            const paginatedReports = reports.slice(offset, offset + limit);

            return {
                reports: paginatedReports,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            logger.error('获取检测报告列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取检测报告详情
     * @param {string} reportId - 报告ID
     * @returns {Object} 报告详情
     */
    getReportById(reportId) {
        try {
            const report = this.repository.findReportById(reportId);

            if (!report) {
                return null;
            }

            // 获取关联的文件
            const files = this.repository.findFilesByReportId(reportId);

            // 修复文件名编码问题
            const fixedFiles = files.map(file => ({
                ...file,
                original_filename: fixFilenameEncoding(file.original_filename)
            }));

            return {
                ...report,
                files: fixedFiles
            };
        } catch (error) {
            logger.error('获取检测报告详情失败:', error);
            throw error;
        }
    }

    /**
     * 根据报告编号获取检测报告
     * @param {string} reportNumber - 报告编号
     * @returns {Object} 报告详情
     */
    getReportByNumber(reportNumber) {
        try {
            const report = this.repository.findReportByNumber(reportNumber);

            if (!report) {
                return null;
            }

            // 获取关联的文件
            const files = this.repository.findFilesByReportId(report.id);

            return {
                ...report,
                files
            };
        } catch (error) {
            logger.error('根据编号获取检测报告失败:', error);
            throw error;
        }
    }

    /**
     * 更新检测报告
     * @param {string} reportId - 报告ID
     * @param {Object} updateData - 更新数据
     * @returns {Object} 更新后的报告信息
     */
    updateReport(reportId, updateData) {
        try {
            const now = new Date().toISOString();

            const reportRecord = {
                title: updateData.title,
                description: updateData.description || '',
                test_type: updateData.testType,
                test_date: updateData.testDate,
                sample_info: updateData.sampleInfo || '',
                test_method: updateData.testMethod || '',
                test_standard: updateData.testStandard || '',
                test_result: updateData.testResult || '',
                conclusion: updateData.conclusion || '',
                status: updateData.status || 'published',
                updated_at: now
            };

            const success = this.repository.updateReport(reportId, reportRecord);

            if (!success) {
                return null;
            }

            return this.getReportById(reportId);
        } catch (error) {
            logger.error('更新检测报告失败:', error);
            throw error;
        }
    }

    /**
     * 删除检测报告
     * @param {string} reportId - 报告ID
     * @returns {boolean} 删除是否成功
     */
    deleteReport(reportId) {
        return this.repository.transaction(() => {
            try {
                // 获取报告信息，包括文件
                const report = this.getReportById(reportId);
                if (!report) {
                    return false;
                }

                // 删除物理文件
                if (report.files && report.files.length > 0) {
                    report.files.forEach(file => {
                        try {
                            if (fs.existsSync(file.file_path)) {
                                fs.unlinkSync(file.file_path);
                            }
                        } catch (error) {
                            logger.warn(`删除文件失败: ${file.file_path}`, error);
                        }
                    });
                }

                // 删除文件记录（由于外键约束，会自动删除）
                // 删除报告记录
                return this.repository.deleteReport(reportId);
            } catch (error) {
                logger.error('删除检测报告失败:', error);
                throw error;
            }
        });
    }

    /**
     * 获取文件信息
     * @param {string} fileId - 文件ID
     * @returns {Object} 文件信息
     */
    getFileById(fileId) {
        try {
            return this.repository.findFileById(fileId);
        } catch (error) {
            logger.error('获取文件信息失败:', error);
            throw error;
        }
    }
}

module.exports = new QualityService();
