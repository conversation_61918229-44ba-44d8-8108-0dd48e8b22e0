-- 完整的PostgreSQL数据库表结构迁移脚本
-- 从SQLite的53个表完整迁移到PostgreSQL

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. 用户表
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(50) PRIMARY KEY,
    usercode VARCHAR(50) UNIQUE NOT NULL,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    department VARCHAR(100),
    email VARCHAR(255),
    active BOOLEAN DEFAULT true,
    permissions JSONB DEFAULT '[]',
    has_signature BOOLEAN DEFAULT false,
    signature_path VARCHAR(500),
    signature_base64 TEXT,
    last_login_at TIMESTAMP,
    last_active_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 2. 申请表
CREATE TABLE IF NOT EXISTS applications (
    id VARCHAR(50) PRIMARY KEY,
    application_number VARCHAR(100) UNIQUE NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    applicant VARCHAR(100) NOT NULL,
    department VARCHAR(100),
    date DATE NOT NULL,
    content TEXT NOT NULL,
    amount VARCHAR(100),
    priority VARCHAR(20) DEFAULT 'normal',
    type VARCHAR(50) DEFAULT 'standard',
    status VARCHAR(20) DEFAULT 'pending',
    current_stage VARCHAR(50),
    need_manager_approval BOOLEAN DEFAULT false,
    need_ceo_approval BOOLEAN DEFAULT true,
    selected_factory_managers JSONB DEFAULT '[]',
    selected_managers JSONB DEFAULT '[]',
    pdf_path VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- 3. 申请附件表
CREATE TABLE IF NOT EXISTS application_attachments (
    id VARCHAR(50) PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (application_id) REFERENCES applications (id)
);

-- 4. 申请草稿表
CREATE TABLE IF NOT EXISTS application_drafts (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    draft_data JSONB NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);



-- 6. 审批历史表
CREATE TABLE IF NOT EXISTS approval_history (
    id VARCHAR(50) PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    approver_id VARCHAR(50) NOT NULL,
    approver_name VARCHAR(100) NOT NULL,
    stage VARCHAR(50) NOT NULL,
    action VARCHAR(20) NOT NULL,
    comments TEXT,
    approved_at TIMESTAMP NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (application_id) REFERENCES applications (id),
    FOREIGN KEY (approver_id) REFERENCES users (id)
);

-- 7. 客户文件表
CREATE TABLE IF NOT EXISTS customer_files (
    id VARCHAR(50) PRIMARY KEY,
    customer_id VARCHAR(50) NOT NULL,
    file_number VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'active',
    created_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (customer_id) REFERENCES file_management_customers (id),
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- 8. 客户文件附件表
CREATE TABLE IF NOT EXISTS customer_file_attachments (
    id VARCHAR(50) PRIMARY KEY,
    customer_file_id VARCHAR(50) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (customer_file_id) REFERENCES customer_files (id)
);

-- 9. 客户文件确认表
CREATE TABLE IF NOT EXISTS customer_file_confirmations (
    id VARCHAR(50) PRIMARY KEY,
    customer_file_id VARCHAR(50) NOT NULL,
    confirmed_by VARCHAR(50) NOT NULL,
    confirmed_at TIMESTAMP NOT NULL DEFAULT NOW(),
    comments TEXT,
    FOREIGN KEY (customer_file_id) REFERENCES customer_files (id),
    FOREIGN KEY (confirmed_by) REFERENCES users (id)
);

-- 10. 客户文件通知表
CREATE TABLE IF NOT EXISTS customer_file_notifications (
    id VARCHAR(50) PRIMARY KEY,
    customer_file_id VARCHAR(50) NOT NULL,
    recipient_id VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    sent_at TIMESTAMP NOT NULL DEFAULT NOW(),
    read_at TIMESTAMP,
    FOREIGN KEY (customer_file_id) REFERENCES customer_files (id),
    FOREIGN KEY (recipient_id) REFERENCES users (id)
);

-- 11. 客户文件编号预留表
CREATE TABLE IF NOT EXISTS customer_file_number_reservations (
    id VARCHAR(50) PRIMARY KEY,
    file_number VARCHAR(100) UNIQUE NOT NULL,
    reserved_by VARCHAR(50) NOT NULL,
    reserved_at TIMESTAMP NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT false,
    FOREIGN KEY (reserved_by) REFERENCES users (id)
);

-- 12. 部门表
CREATE TABLE IF NOT EXISTS departments (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 13. 设备表
CREATE TABLE IF NOT EXISTS equipment (
    id VARCHAR(50) PRIMARY KEY,
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    area VARCHAR(100),
    location VARCHAR(200),
    responsible VARCHAR(100),
    manufacture_date DATE,
    status VARCHAR(20) DEFAULT 'active',
    specifications JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 14. 设备产能配置表
CREATE TABLE IF NOT EXISTS equipment_capabilities (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    product_type VARCHAR(100),
    capacity_per_hour DECIMAL(10,2),
    efficiency_rate DECIMAL(5,2) DEFAULT 100.00,
    setup_time INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);

-- 15. 设备产能表
CREATE TABLE IF NOT EXISTS equipment_capacity (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    capacity_value DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) DEFAULT 'pcs/hour',
    effective_date DATE NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);

-- 16. 设备健康度表
CREATE TABLE IF NOT EXISTS equipment_health (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    health_score DECIMAL(5,2) NOT NULL,
    temperature DECIMAL(8,2),
    vibration DECIMAL(8,2),
    pressure DECIMAL(8,2),
    runtime_hours DECIMAL(10,2),
    maintenance_status VARCHAR(50),
    last_maintenance DATE,
    next_maintenance DATE,
    alerts JSONB DEFAULT '[]',
    recorded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);

-- 17. 设备健康历史表
CREATE TABLE IF NOT EXISTS equipment_health_history (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    health_score DECIMAL(5,2) NOT NULL,
    temperature DECIMAL(8,2),
    vibration DECIMAL(8,2),
    pressure DECIMAL(8,2),
    runtime_hours DECIMAL(10,2),
    maintenance_status VARCHAR(50),
    recorded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    archived_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);

-- 18. 设备维护表
CREATE TABLE IF NOT EXISTS equipment_maintenance (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    type VARCHAR(50) NOT NULL,
    severity_level VARCHAR(50),
    description TEXT,
    maintenance_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    cost DECIMAL(10,2),
    technician VARCHAR(100),
    status VARCHAR(20) DEFAULT 'pending',
    notes TEXT,
    result TEXT,
    reviewer VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);

-- 19. 设备操作员表
CREATE TABLE IF NOT EXISTS equipment_operators (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    operator_id VARCHAR(50) NOT NULL,
    skill_level INTEGER DEFAULT 1,
    certified BOOLEAN DEFAULT false,
    certification_date DATE,
    assigned_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id),
    FOREIGN KEY (operator_id) REFERENCES operators (id)
);

-- 20. 厂区表
CREATE TABLE IF NOT EXISTS factories (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    manager VARCHAR(100),
    contact_phone VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 21. 文件管理附件表
CREATE TABLE IF NOT EXISTS file_management_attachments (
    id VARCHAR(50) PRIMARY KEY,
    file_id VARCHAR(50) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (file_id) REFERENCES file_management_files (id)
);

-- 22. 文件管理客户表
CREATE TABLE IF NOT EXISTS file_management_customers (
    id VARCHAR(50) PRIMARY KEY,
    customer_name VARCHAR(200) NOT NULL,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(50),
    contact_email VARCHAR(255),
    address TEXT,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 23. 文件管理文件表
CREATE TABLE IF NOT EXISTS file_management_files (
    id VARCHAR(50) PRIMARY KEY,
    customer_id VARCHAR(50) NOT NULL,
    file_number VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'active',
    created_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (customer_id) REFERENCES file_management_customers (id),
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- 24. 文件管理通知表
CREATE TABLE IF NOT EXISTS file_management_notifications (
    id VARCHAR(50) PRIMARY KEY,
    file_id VARCHAR(50) NOT NULL,
    recipient_id VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    sent_at TIMESTAMP NOT NULL DEFAULT NOW(),
    read_at TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES file_management_files (id),
    FOREIGN KEY (recipient_id) REFERENCES users (id)
);

-- 25. 文件管理编号预留表
CREATE TABLE IF NOT EXISTS file_management_number_reservations (
    id VARCHAR(50) PRIMARY KEY,
    file_number VARCHAR(100) UNIQUE NOT NULL,
    reserved_by VARCHAR(50) NOT NULL,
    reserved_at TIMESTAMP NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT false,
    FOREIGN KEY (reserved_by) REFERENCES users (id)
);

-- 26. 文件管理产品表
CREATE TABLE IF NOT EXISTS file_management_products (
    id VARCHAR(50) PRIMARY KEY,
    product_name VARCHAR(200) NOT NULL,
    product_code VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    specifications JSONB DEFAULT '{}',
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 27. 库存物品标签表
CREATE TABLE IF NOT EXISTS inventory_item_tags (
    id VARCHAR(50) PRIMARY KEY,
    item_id VARCHAR(50) NOT NULL,
    tag_id VARCHAR(50) NOT NULL,
    assigned_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (item_id) REFERENCES inventory_items (id),
    FOREIGN KEY (tag_id) REFERENCES inventory_tags (id)
);

-- 28. 库存物品表
CREATE TABLE IF NOT EXISTS inventory_items (
    id VARCHAR(50) PRIMARY KEY,
    item_code VARCHAR(100) UNIQUE NOT NULL,
    item_name VARCHAR(200) NOT NULL,
    category VARCHAR(100),
    unit VARCHAR(20) DEFAULT 'pcs',
    current_stock DECIMAL(15,3) DEFAULT 0,
    min_stock DECIMAL(15,3) DEFAULT 0,
    max_stock DECIMAL(15,3),
    unit_price DECIMAL(12,2),
    location VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 29. 库存标签模板表
CREATE TABLE IF NOT EXISTS inventory_label_templates (
    id VARCHAR(50) PRIMARY KEY,
    template_name VARCHAR(100) UNIQUE NOT NULL,
    template_data JSONB NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 30. 库存操作表
CREATE TABLE IF NOT EXISTS inventory_operations (
    id VARCHAR(50) PRIMARY KEY,
    operation_type VARCHAR(20) NOT NULL,
    item_id VARCHAR(50) NOT NULL,
    quantity DECIMAL(15,3) NOT NULL,
    operator_id VARCHAR(50) NOT NULL,
    operation_date TIMESTAMP NOT NULL DEFAULT NOW(),
    notes TEXT,
    FOREIGN KEY (item_id) REFERENCES inventory_items (id),
    FOREIGN KEY (operator_id) REFERENCES users (id)
);

-- 31. 库存记录表
CREATE TABLE IF NOT EXISTS inventory_records (
    id VARCHAR(50) PRIMARY KEY,
    item_id VARCHAR(50) NOT NULL,
    record_type VARCHAR(20) NOT NULL,
    quantity DECIMAL(15,3) NOT NULL,
    balance DECIMAL(15,3) NOT NULL,
    operator_id VARCHAR(50) NOT NULL,
    recorded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    notes TEXT,
    FOREIGN KEY (item_id) REFERENCES inventory_items (id),
    FOREIGN KEY (operator_id) REFERENCES users (id)
);

-- 32. 库存标签表
CREATE TABLE IF NOT EXISTS inventory_tags (
    id VARCHAR(50) PRIMARY KEY,
    tag_name VARCHAR(100) UNIQUE NOT NULL,
    tag_color VARCHAR(20) DEFAULT '#007bff',
    description TEXT,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 33. IP白名单表
CREATE TABLE IF NOT EXISTS ip_whitelist (
    id VARCHAR(50) PRIMARY KEY,
    ip_address INET NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT true,
    created_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- 34. 迁移记录表
CREATE TABLE IF NOT EXISTS migrations (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    executed_at TIMESTAMP NOT NULL DEFAULT NOW(),
    success BOOLEAN NOT NULL DEFAULT true
);



-- 36. 操作员技能表
CREATE TABLE IF NOT EXISTS operator_skills (
    id VARCHAR(50) PRIMARY KEY,
    operator_id VARCHAR(50) NOT NULL,
    equipment_type VARCHAR(100) NOT NULL,
    skill_level INTEGER DEFAULT 1,
    certified BOOLEAN DEFAULT false,
    certification_date DATE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (operator_id) REFERENCES operators (id)
);

-- 37. 性能日志表
CREATE TABLE IF NOT EXISTS performance_logs (
    id VARCHAR(50) PRIMARY KEY,
    operation VARCHAR(100) NOT NULL,
    duration_ms INTEGER NOT NULL,
    memory_usage DECIMAL(10,2),
    cpu_usage DECIMAL(5,2),
    timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
    details JSONB DEFAULT '{}'
);

-- 38. 权限模板表
CREATE TABLE IF NOT EXISTS permission_templates (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '[]',
    is_built_in BOOLEAN DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 39. 产品表
CREATE TABLE IF NOT EXISTS products (
    id VARCHAR(50) PRIMARY KEY,
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    category VARCHAR(100),
    specifications JSONB DEFAULT '{}',
    unit VARCHAR(20) DEFAULT 'pcs',
    standard_time DECIMAL(10,2),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 40. 生产工艺流程表
CREATE TABLE IF NOT EXISTS production_processes (
    id VARCHAR(50) PRIMARY KEY,
    product_id VARCHAR(50) NOT NULL,
    step_number INTEGER NOT NULL,
    step_name VARCHAR(200) NOT NULL,
    equipment_type VARCHAR(100),
    standard_time DECIMAL(10,2),
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (product_id) REFERENCES products (id)
);

-- 41. 质量报告表
CREATE TABLE IF NOT EXISTS quality_reports (
    id VARCHAR(50) PRIMARY KEY,
    report_number VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    product_batch VARCHAR(100),
    test_date DATE NOT NULL,
    inspector VARCHAR(100),
    status VARCHAR(20) DEFAULT 'pending',
    summary TEXT,
    uploaded_by VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (uploaded_by) REFERENCES users (id)
);

-- 42. 质量报告文件表
CREATE TABLE IF NOT EXISTS quality_report_files (
    id VARCHAR(50) PRIMARY KEY,
    report_id VARCHAR(50) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (report_id) REFERENCES quality_reports (id)
);

-- 43. 质量报告编号预留表
CREATE TABLE IF NOT EXISTS quality_report_number_reservations (
    id VARCHAR(50) PRIMARY KEY,
    report_number VARCHAR(100) UNIQUE NOT NULL,
    reserved_by VARCHAR(50) NOT NULL,
    reserved_at TIMESTAMP NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT false,
    FOREIGN KEY (reserved_by) REFERENCES users (id)
);

-- 44. 质量报告编号表
CREATE TABLE IF NOT EXISTS quality_report_numbers (
    id VARCHAR(50) PRIMARY KEY,
    report_number VARCHAR(100) UNIQUE NOT NULL,
    year INTEGER NOT NULL,
    sequence_number INTEGER NOT NULL,
    used BOOLEAN DEFAULT false,
    used_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 45. 资源表
CREATE TABLE IF NOT EXISTS resources (
    id VARCHAR(50) PRIMARY KEY,
    resource_name VARCHAR(200) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    capacity DECIMAL(10,2),
    unit VARCHAR(20),
    location VARCHAR(100),
    status VARCHAR(20) DEFAULT 'available',
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 46. 生产排程表
CREATE TABLE IF NOT EXISTS schedules (
    id VARCHAR(50) PRIMARY KEY,
    schedule_number VARCHAR(100) UNIQUE NOT NULL,
    product_id VARCHAR(50),
    product_name VARCHAR(200) NOT NULL,
    quantity INTEGER NOT NULL,
    unit VARCHAR(20) DEFAULT 'pcs',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    priority VARCHAR(20) DEFAULT 'normal',
    status VARCHAR(20) DEFAULT 'planned',
    equipment_id VARCHAR(50),
    operator_id VARCHAR(50),
    notes TEXT,
    created_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- 47. 排程计划表
CREATE TABLE IF NOT EXISTS schedule_plans (
    id VARCHAR(50) PRIMARY KEY,
    plan_name VARCHAR(200) NOT NULL,
    plan_date DATE NOT NULL,
    total_capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    efficiency DECIMAL(5,2) DEFAULT 100.00,
    status VARCHAR(20) DEFAULT 'draft',
    created_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- 48. 排程进度表
CREATE TABLE IF NOT EXISTS schedule_progress (
    id VARCHAR(50) PRIMARY KEY,
    schedule_id VARCHAR(50) NOT NULL,
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    completed_quantity DECIMAL(10,2) DEFAULT 0,
    quality_score DECIMAL(5,2),
    notes TEXT,
    updated_by VARCHAR(50) NOT NULL,
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (schedule_id) REFERENCES schedules (id),
    FOREIGN KEY (updated_by) REFERENCES users (id)
);

-- 49. 系统指标表
CREATE TABLE IF NOT EXISTS system_metrics (
    id VARCHAR(50) PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,3) NOT NULL,
    metric_unit VARCHAR(20),
    category VARCHAR(50),
    recorded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    details JSONB DEFAULT '{}'
);

-- 50. 用户登录日志表
CREATE TABLE IF NOT EXISTS user_login_logs (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    login_time TIMESTAMP NOT NULL DEFAULT NOW(),
    logout_time TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100),
    login_status VARCHAR(20) DEFAULT 'success',
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- 51. 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_activity TIMESTAMP NOT NULL DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    active BOOLEAN DEFAULT true,
    FOREIGN KEY (user_id) REFERENCES users (id)
);



-- 53. 仓库物料表（与SQLite结构一致）
CREATE TABLE IF NOT EXISTS warehouse_materials (
    id VARCHAR(50) PRIMARY KEY,
    material_code VARCHAR(50) NOT NULL,
    material_name VARCHAR(200) NOT NULL,
    category VARCHAR(100),
    unit VARCHAR(20) DEFAULT 'pcs',
    safety_stock INTEGER,
    current_stock INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 54. 仓库成品表（使用SQLite中的原名）
CREATE TABLE IF NOT EXISTS warehouse_finished_products (
    id VARCHAR(50) PRIMARY KEY,
    product_code VARCHAR(100) UNIQUE NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    batch_number VARCHAR(50),
    boxes_per_unit INTEGER,
    pieces_per_box INTEGER,
    total_pieces INTEGER,
    current_stock INTEGER DEFAULT 0,
    production_date DATE,
    expiry_date DATE,
    location VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 56. 仓库库存事务表（与SQLite结构一致）
CREATE TABLE IF NOT EXISTS warehouse_inventory_transactions (
    id VARCHAR(50) PRIMARY KEY,
    transaction_type VARCHAR(20) NOT NULL,
    item_type VARCHAR(20) NOT NULL,
    item_id VARCHAR(50) NOT NULL,
    quantity INTEGER NOT NULL,
    operator_id VARCHAR(50) NOT NULL,
    transaction_date TIMESTAMP NOT NULL DEFAULT NOW(),
    notes TEXT,
    FOREIGN KEY (operator_id) REFERENCES users (id)
);

-- 58. 仓库二维码表（与SQLite结构一致）
CREATE TABLE IF NOT EXISTS warehouse_qrcodes (
    id VARCHAR(50) PRIMARY KEY,
    qrcode VARCHAR(100) UNIQUE NOT NULL,
    item_type VARCHAR(20) NOT NULL,
    item_id VARCHAR(50) NOT NULL,
    batch_info VARCHAR(100),
    quantity INTEGER,
    status VARCHAR(20) DEFAULT 'active',
    generated_by VARCHAR(50) NOT NULL,
    used_by VARCHAR(50),
    used_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (generated_by) REFERENCES users (id),
    FOREIGN KEY (used_by) REFERENCES users (id)
);

-- 创建索引优化查询性能
-- 用户相关索引
CREATE INDEX IF NOT EXISTS idx_users_usercode ON users (usercode);
CREATE INDEX IF NOT EXISTS idx_users_department ON users (department);
CREATE INDEX IF NOT EXISTS idx_users_role ON users (role);
CREATE INDEX IF NOT EXISTS idx_users_active ON users (active);

-- 申请相关索引
CREATE INDEX IF NOT EXISTS idx_applications_user_id ON applications (user_id);
CREATE INDEX IF NOT EXISTS idx_applications_status ON applications (status);
CREATE INDEX IF NOT EXISTS idx_applications_date ON applications (date);
CREATE INDEX IF NOT EXISTS idx_applications_department ON applications (department);
CREATE INDEX IF NOT EXISTS idx_application_attachments_application_id ON application_attachments (application_id);
CREATE INDEX IF NOT EXISTS idx_application_drafts_user_id ON application_drafts (user_id);

CREATE INDEX IF NOT EXISTS idx_approval_history_application_id ON approval_history (application_id);

-- 客户文件相关索引
CREATE INDEX IF NOT EXISTS idx_customer_files_customer_id ON customer_files (customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_files_status ON customer_files (status);
CREATE INDEX IF NOT EXISTS idx_customer_file_attachments_customer_file_id ON customer_file_attachments (customer_file_id);

-- 设备相关索引
CREATE INDEX IF NOT EXISTS idx_equipment_code ON equipment (code);
CREATE INDEX IF NOT EXISTS idx_equipment_area ON equipment (area);
CREATE INDEX IF NOT EXISTS idx_equipment_status ON equipment (status);
CREATE INDEX IF NOT EXISTS idx_equipment_capabilities_equipment_id ON equipment_capabilities (equipment_id);
CREATE INDEX IF NOT EXISTS idx_equipment_capacity_equipment_id ON equipment_capacity (equipment_id);
CREATE INDEX IF NOT EXISTS idx_equipment_health_equipment_id ON equipment_health (equipment_id);
CREATE INDEX IF NOT EXISTS idx_equipment_health_recorded_at ON equipment_health (recorded_at);
CREATE INDEX IF NOT EXISTS idx_equipment_health_history_equipment_id ON equipment_health_history (equipment_id);
CREATE INDEX IF NOT EXISTS idx_equipment_maintenance_equipment_id ON equipment_maintenance (equipment_id);
CREATE INDEX IF NOT EXISTS idx_equipment_operators_equipment_id ON equipment_operators (equipment_id);

-- 文件管理相关索引
CREATE INDEX IF NOT EXISTS idx_file_management_files_customer_id ON file_management_files (customer_id);
CREATE INDEX IF NOT EXISTS idx_file_management_attachments_file_id ON file_management_attachments (file_id);
CREATE INDEX IF NOT EXISTS idx_file_management_notifications_file_id ON file_management_notifications (file_id);

-- 库存相关索引
CREATE INDEX IF NOT EXISTS idx_inventory_items_item_code ON inventory_items (item_code);
CREATE INDEX IF NOT EXISTS idx_inventory_items_category ON inventory_items (category);
CREATE INDEX IF NOT EXISTS idx_inventory_operations_item_id ON inventory_operations (item_id);
CREATE INDEX IF NOT EXISTS idx_inventory_records_item_id ON inventory_records (item_id);
CREATE INDEX IF NOT EXISTS idx_inventory_item_tags_item_id ON inventory_item_tags (item_id);

-- 产品相关索引
CREATE INDEX IF NOT EXISTS idx_products_code ON products (code);
CREATE INDEX IF NOT EXISTS idx_products_category ON products (category);
CREATE INDEX IF NOT EXISTS idx_production_processes_product_id ON production_processes (product_id);

-- 质量报告相关索引
CREATE INDEX IF NOT EXISTS idx_quality_reports_report_number ON quality_reports (report_number);
CREATE INDEX IF NOT EXISTS idx_quality_reports_test_date ON quality_reports (test_date);
CREATE INDEX IF NOT EXISTS idx_quality_reports_status ON quality_reports (status);
CREATE INDEX IF NOT EXISTS idx_quality_report_files_report_id ON quality_report_files (report_id);

-- 排程相关索引
CREATE INDEX IF NOT EXISTS idx_schedules_product_id ON schedules (product_id);
CREATE INDEX IF NOT EXISTS idx_schedules_status ON schedules (status);
CREATE INDEX IF NOT EXISTS idx_schedules_start_date ON schedules (start_date);
CREATE INDEX IF NOT EXISTS idx_schedule_plans_plan_date ON schedule_plans (plan_date);
CREATE INDEX IF NOT EXISTS idx_schedule_progress_schedule_id ON schedule_progress (schedule_id);

-- 仓库管理相关索引
CREATE INDEX IF NOT EXISTS idx_warehouse_materials_code ON warehouse_materials (material_code);
CREATE INDEX IF NOT EXISTS idx_warehouse_materials_type ON warehouse_materials (material_type);

CREATE INDEX IF NOT EXISTS idx_warehouse_finished_products_code ON warehouse_finished_products (product_code);

CREATE INDEX IF NOT EXISTS idx_warehouse_inventory_transactions_type ON warehouse_inventory_transactions (transaction_type);
CREATE INDEX IF NOT EXISTS idx_warehouse_qrcodes_item ON warehouse_qrcodes (item_type, item_id);
CREATE INDEX IF NOT EXISTS idx_warehouse_qrcodes_status ON warehouse_qrcodes (status);

-- 系统相关索引
CREATE INDEX IF NOT EXISTS idx_user_login_logs_user_id ON user_login_logs (user_id);
CREATE INDEX IF NOT EXISTS idx_user_login_logs_login_time ON user_login_logs (login_time);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions (user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions (expires_at);

CREATE INDEX IF NOT EXISTS idx_performance_logs_operation ON performance_logs (operation);
CREATE INDEX IF NOT EXISTS idx_performance_logs_timestamp ON performance_logs (timestamp);
CREATE INDEX IF NOT EXISTS idx_system_metrics_metric_name ON system_metrics (metric_name);
CREATE INDEX IF NOT EXISTS idx_system_metrics_recorded_at ON system_metrics (recorded_at);