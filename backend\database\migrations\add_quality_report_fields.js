/**
 * 添加质量报告表缺少的字段
 */

require('dotenv').config();
const { postgresConnectionPool } = require('../../utils/postgresConnectionPool');
const logger = require('../../utils/logger');

async function addQualityReportFields() {
    try {
        logger.info('开始添加质量报告表缺少的字段...');
        
        const db = postgresConnectionPool;
        
        // 添加缺少的字段
        const fieldsToAdd = [
            'ADD COLUMN IF NOT EXISTS description TEXT',
            'ADD COLUMN IF NOT EXISTS test_type VARCHAR(100)',
            'ADD COLUMN IF NOT EXISTS sample_info TEXT',
            'ADD COLUMN IF NOT EXISTS test_method TEXT',
            'ADD COLUMN IF NOT EXISTS test_standard TEXT',
            'ADD COLUMN IF NOT EXISTS test_result TEXT',
            'ADD COLUMN IF NOT EXISTS conclusion TEXT',
            'ADD COLUMN IF NOT EXISTS uploaded_at TIMESTAMP DEFAULT NOW()'
        ];
        
        for (const field of fieldsToAdd) {
            try {
                await db.query(`ALTER TABLE quality_reports ${field}`);
                logger.info(`成功添加字段: ${field}`);
            } catch (error) {
                if (error.message.includes('already exists')) {
                    logger.info(`字段已存在，跳过: ${field}`);
                } else {
                    logger.error(`添加字段失败: ${field}`, error);
                }
            }
        }
        
        // 检查表结构
        const tableInfo = await db.query(`
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'quality_reports' 
            ORDER BY ordinal_position
        `);
        
        logger.info('质量报告表当前字段:');
        tableInfo.rows.forEach(col => {
            logger.info(`- ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
        });
        
        logger.info('质量报告表字段添加完成！');
        
    } catch (error) {
        logger.error('添加质量报告表字段失败:', error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    addQualityReportFields()
        .then(() => {
            logger.info('迁移完成');
            process.exit(0);
        })
        .catch(error => {
            logger.error('迁移失败:', error);
            process.exit(1);
        });
}

module.exports = addQualityReportFields;
