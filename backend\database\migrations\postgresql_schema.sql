-- PostgreSQL数据库表结构迁移脚本
-- 从SQLite迁移到PostgreSQL的完整表结构

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(50) PRIMARY KEY,
    usercode VARCHAR(50) UNIQUE NOT NULL,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    department VARCHAR(100),
    email VARCHAR(255),
    active BOOLEAN DEFAULT true,
    permissions JSONB DEFAULT '[]',
    has_signature BOOLEAN DEFAULT false,
    signature_path VARCHAR(500),
    signature_base64 TEXT,
    last_login_at TIMESTAMP,
    last_active_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 申请表
CREATE TABLE IF NOT EXISTS applications (
    id VARCHAR(50) PRIMARY KEY,
    application_number VARCHAR(100) UNIQUE NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    applicant VARCHAR(100) NOT NULL,
    department VARCHAR(100),
    date DATE NOT NULL,
    content TEXT NOT NULL,
    amount VARCHAR(100),
    priority VARCHAR(20) DEFAULT 'normal',
    type VARCHAR(50) DEFAULT 'standard',
    status VARCHAR(20) DEFAULT 'pending',
    current_stage VARCHAR(50),
    need_manager_approval BOOLEAN DEFAULT false,
    need_ceo_approval BOOLEAN DEFAULT true,
    selected_factory_managers JSONB DEFAULT '[]',
    selected_managers JSONB DEFAULT '[]',
    pdf_path VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- 审批记录表
CREATE TABLE IF NOT EXISTS application_approvals (
    id VARCHAR(50) PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    approver_id VARCHAR(50) NOT NULL,
    approver_name VARCHAR(100) NOT NULL,
    stage VARCHAR(50) NOT NULL,
    action VARCHAR(20) NOT NULL,
    comments TEXT,
    approved_at TIMESTAMP NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (application_id) REFERENCES applications (id),
    FOREIGN KEY (approver_id) REFERENCES users (id)
);

-- 生产排程表
CREATE TABLE IF NOT EXISTS schedules (
    id VARCHAR(50) PRIMARY KEY,
    schedule_number VARCHAR(100) UNIQUE NOT NULL,
    product_id VARCHAR(50),
    product_name VARCHAR(200) NOT NULL,
    quantity INTEGER NOT NULL,
    unit VARCHAR(20) DEFAULT 'pcs',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    priority VARCHAR(20) DEFAULT 'normal',
    status VARCHAR(20) DEFAULT 'planned',
    equipment_id VARCHAR(50),
    operator_id VARCHAR(50),
    notes TEXT,
    created_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- 设备表
CREATE TABLE IF NOT EXISTS equipment (
    id VARCHAR(50) PRIMARY KEY,
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    area VARCHAR(100),
    location VARCHAR(200),
    responsible VARCHAR(100),
    manufacture_date DATE,
    status VARCHAR(20) DEFAULT 'active',
    specifications JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 设备产能配置表
CREATE TABLE IF NOT EXISTS equipment_capabilities (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    product_type VARCHAR(100),
    capacity_per_hour DECIMAL(10,2),
    efficiency_rate DECIMAL(5,2) DEFAULT 100.00,
    setup_time INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);

-- 产品表
CREATE TABLE IF NOT EXISTS products (
    id VARCHAR(50) PRIMARY KEY,
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    category VARCHAR(100),
    specifications JSONB DEFAULT '{}',
    unit VARCHAR(20) DEFAULT 'pcs',
    standard_time DECIMAL(10,2),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 生产工艺流程表
CREATE TABLE IF NOT EXISTS production_processes (
    id VARCHAR(50) PRIMARY KEY,
    product_id VARCHAR(50) NOT NULL,
    step_number INTEGER NOT NULL,
    step_name VARCHAR(200) NOT NULL,
    equipment_type VARCHAR(100),
    standard_time DECIMAL(10,2),
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (product_id) REFERENCES products (id)
);

-- 操作员表
CREATE TABLE IF NOT EXISTS operators (
    id VARCHAR(50) PRIMARY KEY,
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    department VARCHAR(100),
    shift VARCHAR(20),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 操作员技能表
CREATE TABLE IF NOT EXISTS operator_skills (
    id VARCHAR(50) PRIMARY KEY,
    operator_id VARCHAR(50) NOT NULL,
    equipment_type VARCHAR(100) NOT NULL,
    skill_level INTEGER DEFAULT 1,
    certified BOOLEAN DEFAULT false,
    certification_date DATE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (operator_id) REFERENCES operators (id)
);

-- 质量报告表
CREATE TABLE IF NOT EXISTS quality_reports (
    id VARCHAR(50) PRIMARY KEY,
    report_number VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    test_type VARCHAR(100),
    test_date DATE NOT NULL,
    sample_info TEXT,
    test_method TEXT,
    test_standard TEXT,
    test_result TEXT,
    conclusion TEXT,
    product_batch VARCHAR(100),
    inspector VARCHAR(100),
    status VARCHAR(20) DEFAULT 'pending',
    summary TEXT,
    uploaded_by VARCHAR(50),
    uploaded_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (uploaded_by) REFERENCES users (id)
);

-- 质量报告文件表
CREATE TABLE IF NOT EXISTS quality_report_files (
    id VARCHAR(50) PRIMARY KEY,
    report_id VARCHAR(50) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (report_id) REFERENCES quality_reports (id)
);

-- 权限模板表
CREATE TABLE IF NOT EXISTS permission_templates (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '[]',
    is_built_in BOOLEAN DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 厂区表
CREATE TABLE IF NOT EXISTS factories (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    manager VARCHAR(100),
    contact_phone VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 部门表
CREATE TABLE IF NOT EXISTS departments (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 文件管理客户表
CREATE TABLE IF NOT EXISTS file_management_customers (
    id VARCHAR(50) PRIMARY KEY,
    customer_name VARCHAR(200) NOT NULL,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(50),
    contact_email VARCHAR(255),
    address TEXT,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_usercode ON users (usercode);
CREATE INDEX IF NOT EXISTS idx_users_department ON users (department);
CREATE INDEX IF NOT EXISTS idx_applications_user_id ON applications (user_id);
CREATE INDEX IF NOT EXISTS idx_applications_status ON applications (status);
CREATE INDEX IF NOT EXISTS idx_applications_date ON applications (date);
CREATE INDEX IF NOT EXISTS idx_application_approvals_application_id ON application_approvals (application_id);
CREATE INDEX IF NOT EXISTS idx_schedules_product_id ON schedules (product_id);
CREATE INDEX IF NOT EXISTS idx_schedules_status ON schedules (status);
CREATE INDEX IF NOT EXISTS idx_schedules_start_date ON schedules (start_date);
CREATE INDEX IF NOT EXISTS idx_equipment_code ON equipment (code);
CREATE INDEX IF NOT EXISTS idx_equipment_area ON equipment (area);
CREATE INDEX IF NOT EXISTS idx_products_code ON products (code);
CREATE INDEX IF NOT EXISTS idx_products_category ON products (category);
CREATE INDEX IF NOT EXISTS idx_quality_reports_report_number ON quality_reports (report_number);
CREATE INDEX IF NOT EXISTS idx_quality_reports_test_date ON quality_reports (test_date);

-- 仓库管理模块表结构

-- 仓库物料表
CREATE TABLE IF NOT EXISTS warehouse_materials (
    id VARCHAR(50) PRIMARY KEY,
    material_code VARCHAR(100) UNIQUE NOT NULL,
    material_name VARCHAR(200) NOT NULL,
    material_type VARCHAR(100),
    unit VARCHAR(20) DEFAULT 'pcs',
    current_stock DECIMAL(15,3) DEFAULT 0,
    min_stock DECIMAL(15,3) DEFAULT 0,
    max_stock DECIMAL(15,3),
    unit_price DECIMAL(12,2),
    supplier VARCHAR(200),
    location VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 仓库成品表
CREATE TABLE IF NOT EXISTS warehouse_products (
    id VARCHAR(50) PRIMARY KEY,
    product_code VARCHAR(100) UNIQUE NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    product_type VARCHAR(100),
    unit VARCHAR(20) DEFAULT 'pcs',
    current_stock DECIMAL(15,3) DEFAULT 0,
    min_stock DECIMAL(15,3) DEFAULT 0,
    max_stock DECIMAL(15,3),
    unit_price DECIMAL(12,2),
    location VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 库存事务表
CREATE TABLE IF NOT EXISTS warehouse_transactions (
    id VARCHAR(50) PRIMARY KEY,
    transaction_number VARCHAR(100) UNIQUE NOT NULL,
    transaction_type VARCHAR(20) NOT NULL, -- inbound, outbound, return, adjust
    item_type VARCHAR(20) NOT NULL, -- material, product
    item_id VARCHAR(50) NOT NULL,
    quantity DECIMAL(15,3) NOT NULL,
    unit_price DECIMAL(12,2),
    total_amount DECIMAL(15,2),
    batch_number VARCHAR(100),
    qrcode VARCHAR(200),
    operator_id VARCHAR(50) NOT NULL,
    notes TEXT,
    transaction_date TIMESTAMP NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (operator_id) REFERENCES users (id)
);

-- 二维码管理表
CREATE TABLE IF NOT EXISTS warehouse_qrcodes (
    id VARCHAR(50) PRIMARY KEY,
    qrcode VARCHAR(200) UNIQUE NOT NULL,
    item_type VARCHAR(20) NOT NULL, -- material, product
    item_id VARCHAR(50) NOT NULL,
    batch_number VARCHAR(100),
    quantity DECIMAL(15,3),
    generated_by VARCHAR(50) NOT NULL,
    generated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    used_at TIMESTAMP,
    used_by VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active', -- active, used, expired
    expires_at TIMESTAMP,
    FOREIGN KEY (generated_by) REFERENCES users (id),
    FOREIGN KEY (used_by) REFERENCES users (id)
);

-- 设备健康度表
CREATE TABLE IF NOT EXISTS equipment_health (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    health_score DECIMAL(5,2) NOT NULL,
    temperature DECIMAL(8,2),
    vibration DECIMAL(8,2),
    pressure DECIMAL(8,2),
    runtime_hours DECIMAL(10,2),
    maintenance_status VARCHAR(50),
    last_maintenance DATE,
    next_maintenance DATE,
    alerts JSONB DEFAULT '[]',
    recorded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);

-- 用户跟踪表
CREATE TABLE IF NOT EXISTS user_tracking (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(50),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100),
    timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- 迁移记录表
CREATE TABLE IF NOT EXISTS migrations (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    executed_at TIMESTAMP NOT NULL DEFAULT NOW(),
    success BOOLEAN NOT NULL DEFAULT true
);

-- 仓库管理模块索引
CREATE INDEX IF NOT EXISTS idx_warehouse_materials_code ON warehouse_materials (material_code);
CREATE INDEX IF NOT EXISTS idx_warehouse_materials_type ON warehouse_materials (material_type);
CREATE INDEX IF NOT EXISTS idx_warehouse_products_code ON warehouse_products (product_code);
CREATE INDEX IF NOT EXISTS idx_warehouse_products_type ON warehouse_products (product_type);
CREATE INDEX IF NOT EXISTS idx_warehouse_transactions_type ON warehouse_transactions (transaction_type);
CREATE INDEX IF NOT EXISTS idx_warehouse_transactions_item ON warehouse_transactions (item_type, item_id);
CREATE INDEX IF NOT EXISTS idx_warehouse_transactions_date ON warehouse_transactions (transaction_date);
CREATE INDEX IF NOT EXISTS idx_warehouse_qrcodes_item ON warehouse_qrcodes (item_type, item_id);
CREATE INDEX IF NOT EXISTS idx_warehouse_qrcodes_status ON warehouse_qrcodes (status);
CREATE INDEX IF NOT EXISTS idx_equipment_health_equipment_id ON equipment_health (equipment_id);
CREATE INDEX IF NOT EXISTS idx_equipment_health_recorded_at ON equipment_health (recorded_at);
CREATE INDEX IF NOT EXISTS idx_user_tracking_user_id ON user_tracking (user_id);
CREATE INDEX IF NOT EXISTS idx_user_tracking_timestamp ON user_tracking (timestamp);
