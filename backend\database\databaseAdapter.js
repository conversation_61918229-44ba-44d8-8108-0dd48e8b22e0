/**
 * 数据库适配器
 * 使用PostgreSQL数据库
 */

require('dotenv').config();
const logger = require('../utils/logger');

class DatabaseAdapter {
    constructor() {
        this.dbType = 'postgresql'; // 固定使用PostgreSQL
        this.repositories = {};
        this.initializeRepositories();
    }

    /**
     * 初始化Repository
     */
    initializeRepositories() {
        try {
            logger.debug('开始初始化 PostgreSQL Repository...');

            // PostgreSQL Repository
            const PostgresUserRepository = require('./postgresUserRepository');
            const PostgresApplicationRepository = require('./postgresApplicationRepository');
            const PostgresEquipmentRepository = require('./postgresEquipmentRepository');
            const PostgresQualityRepository = require('./postgresQualityRepository');
            const PostgresDepartmentRepository = require('./postgresDepartmentRepository');
            const PostgresPermissionTemplateRepository = require('./postgresPermissionTemplateRepository');
            const PostgresProductRepository = require('./postgresProductRepository');
            const PostgresCapacityRepository = require('./postgresCapacityRepository');
            const PostgresScheduleRepository = require('./postgresScheduleRepository');
            const PostgresFileManagementRepository = require('./postgresFileManagementRepository');
            const PostgresMaintenanceRepository = require('./postgresMaintenanceRepository');
            const PostgresHealthRepository = require('./postgresHealthRepository');

            logger.debug('Repository 类导入完成，开始创建实例...');

            this.repositories.user = new PostgresUserRepository();
            this.repositories.application = new PostgresApplicationRepository();
            this.repositories.equipment = new PostgresEquipmentRepository();
            this.repositories.quality = new PostgresQualityRepository();
            this.repositories.department = new PostgresDepartmentRepository();
            this.repositories.permissionTemplate = new PostgresPermissionTemplateRepository();
            this.repositories.product = new PostgresProductRepository();
            this.repositories.capacity = new PostgresCapacityRepository();
            this.repositories.schedule = new PostgresScheduleRepository();
            this.repositories.fileManagement = new PostgresFileManagementRepository();
            this.repositories.maintenance = new PostgresMaintenanceRepository();
            this.repositories.health = new PostgresHealthRepository();

            logger.debug('Repository 实例创建完成', {
                repositoryCount: Object.keys(this.repositories).length,
                hasMaintenanceRepo: !!this.repositories.maintenance
            });

            logger.info('PostgreSQL Repository初始化完成');
        } catch (error) {
            logger.error('Repository初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取用户Repository
     */
    getUserRepository() {
        return this.repositories.user;
    }

    /**
     * 获取申请Repository
     */
    getApplicationRepository() {
        return this.repositories.application;
    }

    /**
     * 获取设备Repository
     */
    getEquipmentRepository() {
        return this.repositories.equipment;
    }

    /**
     * 获取质量Repository
     */
    getQualityRepository() {
        return this.repositories.quality;
    }

    /**
     * 获取部门Repository
     */
    getDepartmentRepository() {
        return this.repositories.department;
    }

    /**
     * 获取权限模板Repository
     */
    getPermissionTemplateRepository() {
        return this.repositories.permissionTemplate;
    }

    /**
     * 获取产品Repository
     */
    getProductRepository() {
        return this.repositories.product;
    }

    /**
     * 获取产能Repository
     */
    getCapacityRepository() {
        return this.repositories.capacity;
    }

    /**
     * 获取排程Repository
     */
    getScheduleRepository() {
        return this.repositories.schedule;
    }

    /**
     * 获取文件管理Repository
     */
    getFileManagementRepository() {
        return this.repositories.fileManagement;
    }

    /**
     * 获取维护Repository
     */
    getMaintenanceRepository() {
        logger.debug('获取 MaintenanceRepository', {
            hasMaintenanceRepo: !!this.repositories.maintenance,
            repositoriesKeys: Object.keys(this.repositories)
        });
        return this.repositories.maintenance;
    }

    /**
     * 获取健康度Repository
     */
    getHealthRepository() {
        return this.repositories.health;
    }

    /**
     * 获取数据库类型
     */
    getDatabaseType() {
        return this.dbType;
    }

    /**
     * 是否使用PostgreSQL
     */
    isPostgreSQL() {
        return true; // 固定返回true
    }

    /**
     * 获取数据库连接信息
     */
    getConnectionInfo() {
        return {
            type: 'PostgreSQL',
            host: process.env.POSTGRES_HOST || 'localhost',
            port: process.env.POSTGRES_PORT || 5432,
            database: process.env.POSTGRES_DATABASE || 'makrite_system',
            user: process.env.POSTGRES_USER || 'postgres'
        };
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        try {
            const { postgresConnectionPool } = require('../utils/postgresConnectionPool');
            return await postgresConnectionPool.testConnection();
        } catch (error) {
            logger.error('数据库连接测试失败:', error);
            return false;
        }
    }

    /**
     * 获取数据库统计信息
     */
    async getStats() {
        try {
            const { postgresConnectionPool } = require('../utils/postgresConnectionPool');
            return postgresConnectionPool.getStats();
        } catch (error) {
            logger.error('获取数据库统计信息失败:', error);
            return null;
        }
    }

    /**
     * 执行数据库迁移
     */
    async runMigrations() {
        try {
            // PostgreSQL迁移逻辑
            logger.info('PostgreSQL数据库迁移已完成');
            return true;
        } catch (error) {
            logger.error('数据库迁移失败:', error);
            throw error;
        }
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        try {
            const { postgresConnectionPool } = require('../utils/postgresConnectionPool');
            await postgresConnectionPool.close();
            logger.info('数据库连接已关闭');
        } catch (error) {
            logger.error('关闭数据库连接失败:', error);
        }
    }
}

// 创建全局数据库适配器实例
const databaseAdapter = new DatabaseAdapter();

module.exports = {
    DatabaseAdapter,
    databaseAdapter
};
