<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备健康度评估 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.js"></script>
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/equipment/health.css">

    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 移动端菜单按钮 -->
        <button @click="toggleSidebar" class="md:hidden fixed top-4 left-4 z-20 bg-white p-2 rounded-md shadow-md border border-gray-200">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- 移动端遮罩层 -->
        <div v-if="sidebarOpen" @click="closeSidebar" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-15"></div>

        <!-- 侧边导航栏 -->
        <sidebar :user="currentUser" :sidebar-open="sidebarOpen"></sidebar>

        <div class="flex-1 ml-0 md:ml-72 p-2 md:p-4">
            <div class="bg-white rounded-lg shadow-md p-3 md:p-6">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 md:mb-6 space-y-3 sm:space-y-0">
                    <h2 class="text-lg md:text-xl font-semibold text-gray-800">设备健康度评估</h2>
                    <div class="flex flex-wrap items-center gap-2 sm:gap-3 w-full sm:w-auto">
                        <button @click="showStandardModal = true"
                                class="px-3 md:px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="hidden sm:inline">评估标准说明</span>
                            <span class="sm:hidden">标准</span>
                        </button>
                        <button @click="batchCalculateHealth"
                                :disabled="loading"
                                class="px-3 md:px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center">
                            <span v-if="loading" class="inline-flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span class="hidden sm:inline">计算中...</span>
                                <span class="sm:hidden">计算中</span>
                            </span>
                            <span v-else>
                                <span class="hidden sm:inline">计算所有设备健康度</span>
                                <span class="sm:hidden">计算全部</span>
                            </span>
                        </button>
                    </div>
                </div>

                <!-- 健康度统计卡片 -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3 md:gap-6">
                    <div class="bg-green-50 p-4 md:p-6 rounded-lg border border-green-200">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 md:h-8 md:w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-3 md:ml-4">
                                <p class="text-xs md:text-sm font-medium text-green-600">优秀</p>
                                <p class="text-xl md:text-2xl font-semibold text-green-900">{{ healthStatistics.distribution.excellent }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-blue-50 p-4 md:p-6 rounded-lg border border-blue-200">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 md:h-8 md:w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-3 md:ml-4">
                                <p class="text-xs md:text-sm font-medium text-blue-600">良好</p>
                                <p class="text-xl md:text-2xl font-semibold text-blue-900">{{ healthStatistics.distribution.good }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-4 md:p-6 rounded-lg border border-yellow-200">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 md:h-8 md:w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <div class="ml-3 md:ml-4">
                                <p class="text-xs md:text-sm font-medium text-yellow-600">一般</p>
                                <p class="text-xl md:text-2xl font-semibold text-yellow-900">{{ healthStatistics.distribution.average }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-orange-50 p-4 md:p-6 rounded-lg border border-orange-200">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 md:h-8 md:w-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-3 md:ml-4">
                                <p class="text-xs md:text-sm font-medium text-orange-600">较差</p>
                                <p class="text-xl md:text-2xl font-semibold text-orange-900">{{ healthStatistics.distribution.poor }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-red-50 p-4 md:p-6 rounded-lg border border-red-200">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 md:h-8 md:w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-3 md:ml-4">
                                <p class="text-xs md:text-sm font-medium text-red-600">危险</p>
                                <p class="text-xl md:text-2xl font-semibold text-red-900">{{ healthStatistics.distribution.dangerous }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 健康度分布图表 -->
                <div class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">健康度分布</h3>
                        <div class="text-sm text-gray-500">
                            实时数据可视化
                        </div>
                    </div>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 环形图 -->
                        <div class="flex justify-center">
                            <div class="relative w-80 h-80">
                                <canvas id="healthDistributionChart"></canvas>
                            </div>
                        </div>
                        <!-- 图例和统计 -->
                        <div class="flex flex-col justify-center space-y-4">
                            <div class="grid grid-cols-1 gap-3">
                                <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                                        <span class="text-sm font-medium text-green-800">优秀 (90-100分)</span>
                                    </div>
                                    <span class="text-lg font-bold text-green-900">{{ healthStatistics.distribution.excellent }}</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                                        <span class="text-sm font-medium text-blue-800">良好 (80-89分)</span>
                                    </div>
                                    <span class="text-lg font-bold text-blue-900">{{ healthStatistics.distribution.good }}</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-yellow-500 rounded-full mr-3"></div>
                                        <span class="text-sm font-medium text-yellow-800">一般 (70-79分)</span>
                                    </div>
                                    <span class="text-lg font-bold text-yellow-900">{{ healthStatistics.distribution.average }}</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-orange-500 rounded-full mr-3"></div>
                                        <span class="text-sm font-medium text-orange-800">较差 (60-69分)</span>
                                    </div>
                                    <span class="text-lg font-bold text-orange-900">{{ healthStatistics.distribution.poor }}</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-red-500 rounded-full mr-3"></div>
                                        <span class="text-sm font-medium text-red-800">危险 (0-59分)</span>
                                    </div>
                                    <span class="text-lg font-bold text-red-900">{{ healthStatistics.distribution.dangerous }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 总体概览 -->
                <div class="mt-4 md:mt-6 grid grid-cols-1 sm:grid-cols-3 gap-3 md:gap-6">
                    <div class="bg-gray-50 p-4 md:p-6 rounded-lg">
                        <div class="text-center">
                            <p class="text-xs md:text-sm font-medium text-gray-600">设备总数</p>
                            <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ healthStatistics.overview.totalEquipment }}</p>
                        </div>
                    </div>
                    <div class="bg-gray-50 p-4 md:p-6 rounded-lg">
                        <div class="text-center">
                            <p class="text-xs md:text-sm font-medium text-gray-600">平均健康度</p>
                            <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ healthStatistics.overview.averageHealth }}</p>
                        </div>
                    </div>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <div class="text-center">
                            <p class="text-sm font-medium text-gray-600">需要关注</p>
                            <p class="text-3xl font-bold text-red-600">{{ healthStatistics.warningEquipment.length }}</p>
                        </div>
                    </div>
                </div>

                <!-- 健康度趋势分析和厂区对比 -->
                <div class="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 健康度趋势图表 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">健康度趋势分析</h3>
                            <div class="flex items-center space-x-2">
                                <select v-model="trendPeriod" @change="updateTrendChart" class="text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="7">近7天</option>
                                    <option value="30">近30天</option>
                                    <option value="90">近90天</option>
                                </select>
                            </div>
                        </div>
                        <div class="h-64">
                            <canvas id="healthTrendChart"></canvas>
                        </div>
                    </div>

                    <!-- 厂区健康度对比图表 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">厂区健康度对比</h3>
                            <div class="text-sm text-gray-500">
                                各厂区平均健康度对比分析
                            </div>
                        </div>
                        <div class="h-64">
                            <canvas id="areaComparisonChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 需要关注的设备 -->
                <div v-if="healthStatistics.warningEquipment.length > 0" class="mt-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">需要关注的设备</h3>
                    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备信息</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">健康度</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">等级</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">紧急建议</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="equipment in healthStatistics.warningEquipment" :key="equipment.equipmentId">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">{{ equipment.equipmentName }}</div>
                                                <div class="text-sm text-gray-500">{{ equipment.equipmentCode }} - {{ equipment.area }}</div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ equipment.totalScore }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="healthLevelColor(equipment.totalScore)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ equipment.level }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ equipment.urgentRecommendations }} 条</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button @click="showEquipmentDetail(equipment)"
                                                    class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                            <button @click="handleCalculateHealth($event, equipment.equipmentId)"
                                                    :disabled="isCalculating(equipment.equipmentId)"
                                                    type="button"
                                                    class="text-green-600 hover:text-green-900 disabled:opacity-50 disabled:cursor-not-allowed">
                                                <span v-if="isCalculating(equipment.equipmentId)" class="inline-flex items-center">
                                                    <svg class="animate-spin -ml-1 mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24">
                                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                    计算中...
                                                </span>
                                                <span v-else>重新计算</span>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 设备卡片网格 -->
                <div class="mt-8">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-gray-900">设备健康度监控</h3>
                        <div class="flex items-center space-x-3">
                            <!-- 视图切换按钮 -->
                            <div class="flex bg-gray-100 rounded-lg p-1">
                                <button @click="viewMode = 'cards'"
                                        :class="viewMode === 'cards' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'"
                                        class="px-3 py-1 rounded-md text-sm font-medium transition-all duration-200">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                    </svg>
                                    卡片
                                </button>
                                <button @click="viewMode = 'table'"
                                        :class="viewMode === 'table' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'"
                                        class="px-3 py-1 rounded-md text-sm font-medium transition-all duration-200">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 6h18m-9 8h9"></path>
                                    </svg>
                                    列表
                                </button>
                            </div>

                            <!-- 展开/折叠全部按钮 -->
                            <div class="flex items-center gap-2">
                                <button @click="expandAll"
                                        :disabled="isAllExpanded"
                                        :class="[
                                            'px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                                            isAllExpanded
                                                ? 'text-blue-600 bg-blue-50 border border-blue-200 cursor-default'
                                                : 'text-gray-600 bg-gray-50 border border-gray-200 hover:bg-gray-100 hover:text-blue-600'
                                        ]">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                    展开全部
                                </button>
                                <button @click="collapseAll"
                                        :disabled="isAllCollapsed"
                                        :class="[
                                            'px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                                            isAllCollapsed
                                                ? 'text-blue-600 bg-blue-50 border border-blue-200 cursor-default'
                                                : 'text-gray-600 bg-gray-50 border border-gray-200 hover:bg-gray-100 hover:text-blue-600'
                                        ]">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                    </svg>
                                    折叠全部
                                </button>
                            </div>

                            <!-- 筛选和搜索 -->
                            <div class="relative">
                                <input v-model="searchQuery" type="text" placeholder="搜索设备..."
                                       class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64">
                                <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>

                            <button @click="showFilters = !showFilters"
                                    class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                                </svg>
                                筛选
                            </button>
                        </div>
                    </div>

                    <!-- 筛选器面板 -->
                    <div v-if="showFilters" class="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">区域</label>
                                <select v-model="filters.area" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">所有区域</option>
                                    <option v-for="area in availableAreas" :key="area" :value="area">{{ area }}</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">健康度等级</label>
                                <select v-model="filters.healthLevel" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">所有等级</option>
                                    <option value="excellent">优秀 (90-100)</option>
                                    <option value="good">良好 (80-89)</option>
                                    <option value="average">一般 (70-79)</option>
                                    <option value="poor">较差 (60-69)</option>
                                    <option value="dangerous">危险 (0-59)</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">设备状态</label>
                                <select v-model="filters.status" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">所有状态</option>
                                    <option v-for="status in availableStatuses" :key="status" :value="status">{{ getStatusDisplayName(status) }}</option>
                                </select>
                            </div>
                            <div class="flex items-end">
                                <button @click="resetFilters" class="w-full px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors">
                                    重置筛选
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 卡片视图 - 按厂区分组 -->
                    <div v-if="viewMode === 'cards'" class="space-y-8">
                        <div v-for="(areaGroup, index) in groupedEquipmentByArea" :key="areaGroup.area" class="area-group">
                            <!-- 厂区头部 -->
                            <div class="area-header sticky top-0 z-10 bg-white border rounded-xl shadow-sm mb-6 overflow-hidden"
                                 :class="[
                                     getAreaHealthColor(areaGroup.averageHealth),
                                     isDragging ? 'cursor-grabbing' : 'cursor-pointer',
                                     isDragging && index === draggedAreaIndex ? 'dragging' : '',
                                     'transition-all duration-200 select-none'
                                 ]"
                                 @mousedown="handleAreaMouseDown($event, index)"
                                 @touchstart="handleAreaMouseDown($event, index)"
                                 :draggable="false"
                                 @selectstart.prevent
                                 @contextmenu.prevent>

                                <div class="p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-4">
                                            <!-- 展开/折叠图标 -->
                                            <div class="flex items-center">
                                                <svg class="w-5 h-5 transition-transform duration-200"
                                                     :class="areaGroup.collapsed ? 'transform rotate-0' : 'transform rotate-90'"
                                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </div>

                                            <!-- 厂区信息 -->
                                            <div>
                                                <h3 class="text-lg font-semibold">{{ areaGroup.area }}</h3>
                                                <p class="text-sm opacity-75">{{ areaGroup.count }} 台设备</p>
                                            </div>

                                            <!-- 拖拽手柄图标 -->
                                            <div class="flex items-center text-gray-400 hover:text-gray-600 transition-colors">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                                                </svg>
                                                <svg class="w-5 h-5 -ml-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                                                </svg>
                                                <span v-if="isDragging" class="ml-2 text-xs font-medium">拖拽中...</span>
                                                <span v-else class="ml-2 text-xs">长按拖拽</span>
                                            </div>
                                        </div>

                                        <!-- 厂区统计信息 -->
                                        <div class="flex items-center space-x-6">
                                            <!-- 平均健康度 -->
                                            <div class="text-center">
                                                <div class="text-2xl font-bold">{{ areaGroup.averageHealth }}</div>
                                                <div class="text-xs opacity-75">平均健康度</div>
                                            </div>

                                            <!-- 健康度分布 -->
                                            <div class="flex items-center space-x-1">
                                                <!-- 优秀设备 -->
                                                <div class="rounded-full transition-all duration-200"
                                                     :class="[
                                                         areaGroup.equipment.filter(eq => getHealthScore(eq) >= 90).length > 0 ? 'bg-green-500' : 'bg-gray-300',
                                                         getHealthDotSize(areaGroup.equipment.filter(eq => getHealthScore(eq) >= 90).length, areaGroup.equipment.length)
                                                     ]"
                                                     :title="`优秀设备: ${areaGroup.equipment.filter(eq => getHealthScore(eq) >= 90).length}台`"></div>
                                                <!-- 良好设备 -->
                                                <div class="rounded-full transition-all duration-200"
                                                     :class="[
                                                         areaGroup.equipment.filter(eq => getHealthScore(eq) >= 80 && getHealthScore(eq) < 90).length > 0 ? 'bg-blue-500' : 'bg-gray-300',
                                                         getHealthDotSize(areaGroup.equipment.filter(eq => getHealthScore(eq) >= 80 && getHealthScore(eq) < 90).length, areaGroup.equipment.length)
                                                     ]"
                                                     :title="`良好设备: ${areaGroup.equipment.filter(eq => getHealthScore(eq) >= 80 && getHealthScore(eq) < 90).length}台`"></div>
                                                <!-- 一般设备 -->
                                                <div class="rounded-full transition-all duration-200"
                                                     :class="[
                                                         areaGroup.equipment.filter(eq => getHealthScore(eq) >= 70 && getHealthScore(eq) < 80).length > 0 ? 'bg-yellow-500' : 'bg-gray-300',
                                                         getHealthDotSize(areaGroup.equipment.filter(eq => getHealthScore(eq) >= 70 && getHealthScore(eq) < 80).length, areaGroup.equipment.length)
                                                     ]"
                                                     :title="`一般设备: ${areaGroup.equipment.filter(eq => getHealthScore(eq) >= 70 && getHealthScore(eq) < 80).length}台`"></div>
                                                <!-- 较差设备 -->
                                                <div class="rounded-full transition-all duration-200"
                                                     :class="[
                                                         areaGroup.equipment.filter(eq => getHealthScore(eq) >= 60 && getHealthScore(eq) < 70).length > 0 ? 'bg-orange-500' : 'bg-gray-300',
                                                         getHealthDotSize(areaGroup.equipment.filter(eq => getHealthScore(eq) >= 60 && getHealthScore(eq) < 70).length, areaGroup.equipment.length)
                                                     ]"
                                                     :title="`较差设备: ${areaGroup.equipment.filter(eq => getHealthScore(eq) >= 60 && getHealthScore(eq) < 70).length}台`"></div>
                                                <!-- 危险设备 -->
                                                <div class="rounded-full transition-all duration-200"
                                                     :class="[
                                                         areaGroup.equipment.filter(eq => getHealthScore(eq) < 60).length > 0 ? 'bg-red-500' : 'bg-gray-300',
                                                         getHealthDotSize(areaGroup.equipment.filter(eq => getHealthScore(eq) < 60).length, areaGroup.equipment.length)
                                                     ]"
                                                     :title="`危险设备: ${areaGroup.equipment.filter(eq => getHealthScore(eq) < 60).length}台`"></div>
                                            </div>


                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 设备卡片网格 -->
                            <div v-if="!areaGroup.collapsed"
                                 class="equipment-cards grid gap-6 transition-all duration-300"
                                 :class="areaGroup.collapsed ? 'opacity-0 max-h-0 overflow-hidden' : 'opacity-100'">
                                <div v-for="equipment in areaGroup.equipment" :key="equipment.id"
                                     class="equipment-card bg-white rounded-xl shadow-sm border border-gray-200 cursor-pointer group"
                                     :class="{
                                         'ring-2 ring-red-500 ring-opacity-50 border-red-200': getHealthScore(equipment) !== null && getHealthScore(equipment) < 60,
                                         'ring-2 ring-orange-500 ring-opacity-50 border-orange-200': getHealthScore(equipment) !== null && getHealthScore(equipment) >= 60 && getHealthScore(equipment) < 70,
                                         'ring-2 ring-yellow-500 ring-opacity-50 border-yellow-200': getHealthScore(equipment) !== null && getHealthScore(equipment) >= 70 && getHealthScore(equipment) < 80,
                                         'ring-2 ring-blue-500 ring-opacity-50 border-blue-200': getHealthScore(equipment) !== null && getHealthScore(equipment) >= 80 && getHealthScore(equipment) < 90,
                                         'ring-2 ring-green-500 ring-opacity-50 border-green-200': getHealthScore(equipment) !== null && getHealthScore(equipment) >= 90
                                     }"
                                     @click="showEquipmentDetail(equipment)">

                            <!-- 卡片头部 -->
                            <div class="p-6 pb-4">
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex-1">
                                        <h4 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                                            {{ equipment.name }}
                                        </h4>
                                        <p class="text-sm text-gray-500 mt-1">{{ equipment.code }}</p>
                                    </div>

                                    <!-- 状态指示器 -->
                                    <div class="flex items-center space-x-2">
                                        <div class="relative">
                                            <div class="w-3 h-3 rounded-full"
                                                 :class="getStatusIndicatorColor(equipment.status)"></div>
                                            <div v-if="equipment.status === 'running'"
                                                 class="absolute inset-0 w-3 h-3 rounded-full bg-green-400 animate-ping"></div>
                                        </div>
                                        <span class="text-xs font-medium px-2 py-1 rounded-full"
                                              :class="getStatusBadgeColor(equipment.status)">
                                            {{ getStatusText(equipment.status) }}
                                        </span>
                                    </div>
                                </div>

                                <!-- 设备信息 -->
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-500">类型:</span>
                                        <span class="ml-1 font-medium text-gray-900">{{ equipment.type || '未知' }}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">区域:</span>
                                        <span class="ml-1 font-medium text-gray-900">{{ equipment.area || '未知' }}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">负责人:</span>
                                        <span class="ml-1 font-medium text-gray-900">{{ equipment.responsible || '未分配' }}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">位置:</span>
                                        <span class="ml-1 font-medium text-gray-900">{{ equipment.location || '未知' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 健康度显示区域 -->
                            <div class="px-6 pb-4">
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <span class="text-sm font-medium text-gray-700">健康度评分</span>
                                        <span class="text-xs text-gray-500">
                                            {{ equipment.lastAssessment ? formatDate(equipment.lastAssessment) : '未评估' }}
                                        </span>
                                    </div>

                                    <!-- 健康度分数和等级 -->
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="text-3xl font-bold" :class="getHealthScoreColor(getHealthScore(equipment))">
                                            {{ getHealthScore(equipment) || '--' }}
                                        </div>
                                        <div class="text-right">
                                            <div class="text-sm font-medium" :class="getHealthScoreColor(getHealthScore(equipment))">
                                                {{ getHealthLevel(getHealthScore(equipment)) }}
                                            </div>
                                            <div class="text-xs text-gray-500 mt-1">
                                                {{ getHealthScore(equipment) ? '分' : '' }}
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 健康度进度条 -->
                                    <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                                        <div class="health-progress h-2 rounded-full"
                                             :class="getHealthBarColor(getHealthScore(equipment))"
                                             :style="{ width: `${getHealthScore(equipment) || 0}%` }"></div>
                                    </div>

                                    <!-- 四维度快速预览 -->
                                    <div v-if="equipment.healthMetrics" class="grid grid-cols-4 gap-2 text-xs">
                                        <div class="text-center">
                                            <div class="text-blue-600 font-semibold">{{ equipment.healthMetrics.age || '--' }}</div>
                                            <div class="text-gray-500">年龄</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-green-600 font-semibold">{{ equipment.healthMetrics.repair || '--' }}</div>
                                            <div class="text-gray-500">维修</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-orange-600 font-semibold">{{ equipment.healthMetrics.fault || '--' }}</div>
                                            <div class="text-gray-500">故障</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-purple-600 font-semibold">{{ equipment.healthMetrics.maintenance || '--' }}</div>
                                            <div class="text-gray-500">保养</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 卡片底部操作区 -->
                            <div class="px-6 pb-6">
                                <div class="flex items-center justify-between space-x-2">
                                    <button @click="handleCalculateHealth($event, equipment.id)"
                                            :disabled="isCalculating(equipment.id)"
                                            type="button"
                                            class="flex-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium">
                                        <svg v-if="isCalculating(equipment.id)" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        {{ isCalculating(equipment.id) ? '计算中...' : '重新计算' }}
                                    </button>

                                    <button @click.stop="showEquipmentDetail(equipment)"
                                            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium">
                                        详情
                                    </button>
                                </div>

                                <!-- 预警提示 -->
                                <div v-if="getHealthScore(equipment) < 60" class="mt-3 p-2 bg-red-50 border border-red-200 rounded-lg">
                                    <div class="flex items-center text-red-700">
                                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-xs font-medium">需要立即关注</span>
                                    </div>
                                </div>

                                <div v-else-if="getHealthScore(equipment) >= 60 && getHealthScore(equipment) < 80" class="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <div class="flex items-center text-yellow-700">
                                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-xs font-medium">建议安排维护</span>
                                    </div>
                                </div>
                            </div>
                                </div>
                            </div>

                            <!-- 厂区分页控件 -->
                            <div v-if="!areaGroup.collapsed && areaGroup.hasMultiplePages && viewMode === 'cards'"
                                 class="mt-6 flex items-center justify-center">
                                <div class="flex items-center space-x-2">
                                    <!-- 上一页 -->
                                    <button @click="goToAreaPage(areaGroup.area, areaGroup.currentPage - 1)"
                                            :disabled="areaGroup.currentPage <= 1"
                                            :class="['px-3 py-1 rounded-md border', areaGroup.currentPage <= 1 ? 'border-gray-200 text-gray-400 cursor-not-allowed' : 'border-gray-300 text-gray-700 hover:bg-gray-50']">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                        </svg>
                                    </button>

                                    <!-- 动态分页按钮 -->
                                    <template v-if="areaGroup.totalPages <= 7">
                                        <button v-for="page in areaGroup.totalPages" :key="page"
                                                @click="goToAreaPage(areaGroup.area, page)"
                                                :class="['px-3 py-1 rounded-md', areaGroup.currentPage === page ? 'bg-blue-500 text-white' : 'border border-gray-300 text-gray-700 hover:bg-gray-50']">
                                            {{ page }}
                                        </button>
                                    </template>
                                    <template v-else>
                                        <!-- 前部分页码 -->
                                        <button v-if="areaGroup.currentPage > 3" @click="goToAreaPage(areaGroup.area, 1)"
                                                class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50">
                                            1
                                        </button>
                                        <span v-if="areaGroup.currentPage > 4" class="px-1 text-gray-500">...</span>

                                        <!-- 中间页码 -->
                                        <button v-for="page in 3" :key="page"
                                                v-if="areaGroup.currentPage - 2 + page > 0 && areaGroup.currentPage - 2 + page <= areaGroup.totalPages"
                                                @click="goToAreaPage(areaGroup.area, areaGroup.currentPage - 2 + page)"
                                                :class="['px-3 py-1 rounded-md', areaGroup.currentPage === (areaGroup.currentPage - 2 + page) ? 'bg-blue-500 text-white' : 'border border-gray-300 text-gray-700 hover:bg-gray-50']">
                                            {{ areaGroup.currentPage - 2 + page }}
                                        </button>

                                        <!-- 后部分页码 -->
                                        <span v-if="areaGroup.currentPage < areaGroup.totalPages - 3" class="px-1 text-gray-500">...</span>
                                        <button v-if="areaGroup.currentPage < areaGroup.totalPages - 2" @click="goToAreaPage(areaGroup.area, areaGroup.totalPages)"
                                                class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50">
                                            {{ areaGroup.totalPages }}
                                        </button>
                                    </template>

                                    <!-- 下一页 -->
                                    <button @click="goToAreaPage(areaGroup.area, areaGroup.currentPage + 1)"
                                            :disabled="areaGroup.currentPage >= areaGroup.totalPages"
                                            :class="['px-3 py-1 rounded-md border', areaGroup.currentPage >= areaGroup.totalPages ? 'border-gray-200 text-gray-400 cursor-not-allowed' : 'border-gray-300 text-gray-700 hover:bg-gray-50']">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 表格视图 -->
                    <div v-if="viewMode === 'table'" class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                            @click="sortBy('name')">
                                            <div class="flex items-center">
                                                设备信息
                                                <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                                </svg>
                                            </div>
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">健康度</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">区域</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后评估</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="equipment in filteredEquipmentList" :key="equipment.id"
                                        class="hover:bg-gray-50 transition-colors duration-200"
                                        :class="{ 'bg-red-50 border-l-4 border-red-400': getHealthScore(equipment) < 60 }">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <!-- 健康度指示器 -->
                                                <div class="flex-shrink-0 mr-3">
                                                    <div class="w-3 h-3 rounded-full"
                                                         :class="getHealthIndicatorColor(getHealthScore(equipment))"></div>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">{{ equipment.name }}</div>
                                                    <div class="text-sm text-gray-500">{{ equipment.code }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-1">
                                                    <div class="flex items-center justify-between mb-1">
                                                        <span class="text-sm font-medium text-gray-900">{{ getHealthScore(equipment) || '--' }}</span>
                                                        <span class="text-xs" :class="getHealthScoreColor(getHealthScore(equipment))">
                                                            {{ getHealthLevel(getHealthScore(equipment)) }}
                                                        </span>
                                                    </div>
                                                    <!-- 健康度进度条 -->
                                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                                        <div class="h-2 rounded-full transition-all duration-300"
                                                             :class="getHealthBarColor(getHealthScore(equipment))"
                                                             :style="{ width: `${getHealthScore(equipment) || 0}%` }"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                                  :class="getStatusBadgeColor(equipment.status)">
                                                {{ getStatusText(equipment.status) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ equipment.area || '未知' }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ equipment.lastAssessment ? formatDate(equipment.lastAssessment) : '未评估' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex items-center space-x-2">
                                                <button @click="showEquipmentDetail(equipment)"
                                                        class="text-blue-600 hover:text-blue-900 px-3 py-1 rounded-md hover:bg-blue-50 transition-colors">
                                                    详情
                                                </button>
                                                <button @click="handleCalculateHealth($event, equipment.id)"
                                                        :disabled="isCalculating(equipment.id)"
                                                        type="button"
                                                        class="text-green-600 hover:text-green-900 px-3 py-1 rounded-md hover:bg-green-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                                                    <span v-if="isCalculating(equipment.id)" class="inline-flex items-center">
                                                        <svg class="animate-spin -ml-1 mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24">
                                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                        </svg>
                                                        计算中...
                                                    </span>
                                                    <span v-else>重新计算</span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页控件 -->
                        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                            <div class="flex items-center justify-between">
                                <!-- 左侧：显示信息 -->
                                <div class="text-sm text-gray-700">
                                    显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalEquipment) }}
                                    共 {{ totalEquipment }} 条记录
                                </div>

                                <!-- 右侧：分页控件 -->
                                <div class="flex items-center space-x-2">
                                    <!-- 上一页 -->
                                    <button @click="goToPage(currentPage - 1)"
                                            :disabled="currentPage <= 1"
                                            :class="['px-3 py-1 rounded-md border', currentPage <= 1 ? 'border-gray-200 text-gray-400 cursor-not-allowed' : 'border-gray-300 text-gray-700 hover:bg-gray-50']">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                        </svg>
                                    </button>

                                    <!-- 动态分页按钮 -->
                                    <template v-if="totalPages <= 7">
                                        <button v-for="page in totalPages" :key="page" @click="goToPage(page)"
                                                :class="['px-3 py-1 rounded-md', currentPage === page ? 'bg-blue-500 text-white' : 'border border-gray-300 text-gray-700 hover:bg-gray-50']">
                                            {{ page }}
                                        </button>
                                    </template>
                                    <template v-else>
                                        <!-- 前部分页码 -->
                                        <button v-if="currentPage > 3" @click="goToPage(1)"
                                                class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50">
                                            1
                                        </button>
                                        <span v-if="currentPage > 4" class="px-1 text-gray-500">...</span>

                                        <!-- 中间页码 -->
                                        <button v-for="page in 3" :key="page"
                                                v-if="currentPage - 2 + page > 0 && currentPage - 2 + page <= totalPages"
                                                @click="goToPage(currentPage - 2 + page)"
                                                :class="['px-3 py-1 rounded-md', currentPage === (currentPage - 2 + page) ? 'bg-blue-500 text-white' : 'border border-gray-300 text-gray-700 hover:bg-gray-50']">
                                            {{ currentPage - 2 + page }}
                                        </button>

                                        <!-- 后部分页码 -->
                                        <span v-if="currentPage < totalPages - 3" class="px-1 text-gray-500">...</span>
                                        <button v-if="currentPage < totalPages - 2" @click="goToPage(totalPages)"
                                                class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50">
                                            {{ totalPages }}
                                        </button>
                                    </template>

                                    <!-- 下一页 -->
                                    <button @click="goToPage(currentPage + 1)"
                                            :disabled="currentPage >= totalPages"
                                            :class="['px-3 py-1 rounded-md border', currentPage >= totalPages ? 'border-gray-200 text-gray-400 cursor-not-allowed' : 'border-gray-300 text-gray-700 hover:bg-gray-50']">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 空状态 -->
                    <div v-if="filteredEquipmentList.length === 0" class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">没有找到设备</h3>
                        <p class="mt-1 text-sm text-gray-500">请尝试调整搜索条件或筛选器</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 拖拽提示 -->
        <div v-if="isDragging" class="drag-hint">
            拖拽厂区到目标位置后松开鼠标
        </div>

        <!-- 设备健康度详情模态框 -->
        <div v-if="showDetailModal" class="modal-container fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="showDetailModal = false">
            <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white" @click.stop>
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">设备健康度详情</h3>
                    <button @click="showDetailModal = false" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div v-if="healthDetail" class="space-y-6">
                    <!-- 总体健康度 -->
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <div class="text-center">
                            <div class="text-4xl font-bold mb-2" :class="healthLevelColor(healthDetail.overallHealth)">
                                {{ healthDetail.overallHealth }}
                            </div>
                            <div class="text-lg font-medium text-gray-600">
                                {{ healthLevelText(healthDetail.overallHealth) }}
                            </div>
                            <div class="text-sm text-gray-500 mt-2">
                                最后评估: {{ new Date(healthDetail.lastAssessment).toLocaleString() }}
                            </div>
                        </div>
                    </div>

                    <!-- 标签页导航 -->
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8">
                            <button @click="activeTab = 'overview'"
                                    :class="activeTab === 'overview' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                概览
                            </button>
                            <button @click="activeTab = 'calculation'"
                                    :class="activeTab === 'calculation' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                计算详情
                            </button>
                            <button @click="activeTab = 'data'"
                                    :class="activeTab === 'data' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                数据来源
                            </button>
                        </nav>
                    </div>

                    <!-- 概览标签页 -->
                    <div v-if="activeTab === 'overview'" class="space-y-6">
                        <!-- 四维度评分 -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div class="bg-white p-4 rounded-lg border border-gray-200 text-center">
                                <div class="text-2xl font-bold text-blue-600">{{ healthDetail.metrics.age }}</div>
                                <div class="text-sm text-gray-600">设备年龄</div>
                                <div class="text-xs text-gray-400 mt-1">权重: 20%</div>
                            </div>
                            <div class="bg-white p-4 rounded-lg border border-gray-200 text-center">
                                <div class="text-2xl font-bold text-green-600">{{ healthDetail.metrics.repairFrequency }}</div>
                                <div class="text-sm text-gray-600">维修频率</div>
                                <div class="text-xs text-gray-400 mt-1">权重: 30%</div>
                            </div>
                            <div class="bg-white p-4 rounded-lg border border-gray-200 text-center">
                                <div class="text-2xl font-bold text-orange-600">{{ healthDetail.metrics.faultSeverity }}</div>
                                <div class="text-sm text-gray-600">故障严重程度</div>
                                <div class="text-xs text-gray-400 mt-1">权重: 30%</div>
                            </div>
                            <div class="bg-white p-4 rounded-lg border border-gray-200 text-center">
                                <div class="text-2xl font-bold text-purple-600">{{ healthDetail.metrics.maintenance }}</div>
                                <div class="text-sm text-gray-600">保养情况</div>
                                <div class="text-xs text-gray-400 mt-1">权重: 20%</div>
                            </div>
                        </div>

                        <!-- 总分计算公式 -->
                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <h4 class="font-medium text-blue-800 mb-2">总分计算公式</h4>
                            <div class="text-sm text-blue-700 font-mono">
                                总分 = 设备年龄 × 20% + 维修频率 × 30% + 故障严重程度 × 30% + 保养情况 × 20%
                            </div>
                            <div class="text-sm text-blue-700 font-mono mt-2">
                                = {{ healthDetail.metrics.age }} × 0.2 + {{ healthDetail.metrics.repairFrequency }} × 0.3 + {{ healthDetail.metrics.faultSeverity }} × 0.3 + {{ healthDetail.metrics.maintenance }} × 0.2
                            </div>
                            <div class="text-sm text-blue-700 font-mono mt-2">
                                = {{ Math.round(healthDetail.metrics.age * 0.2 + healthDetail.metrics.repairFrequency * 0.3 + healthDetail.metrics.faultSeverity * 0.3 + healthDetail.metrics.maintenance * 0.2) }}
                            </div>
                        </div>
                    </div>

                    <!-- 计算详情标签页 -->
                    <div v-if="activeTab === 'calculation'" class="space-y-6">
                        <!-- 设备年龄详情 -->
                        <div v-if="healthDetail.dimensions && healthDetail.dimensions.age" class="bg-white p-6 rounded-lg border border-gray-200">
                            <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <span class="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                                设备年龄评分详情 (权重: 20%)
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <div class="text-sm text-gray-600 mb-2">基本信息</div>
                                    <div class="space-y-1 text-sm">
                                        <div>设备年龄: <span class="font-medium">{{ healthDetail.dimensions.age.details?.ageInYears || 'N/A' }} 年</span></div>
                                        <div>年龄分类: <span class="font-medium">{{ healthDetail.dimensions.age.details?.category || 'N/A' }}</span></div>
                                        <div>评分: <span class="font-medium text-blue-600">{{ healthDetail.dimensions.age.score }} 分</span></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-600 mb-2">评分标准</div>
                                    <div class="text-xs text-gray-500 space-y-1">
                                        <div>• 不足1年: 100分</div>
                                        <div>• 1-3年: 90分</div>
                                        <div>• 3-5年: 80分</div>
                                        <div>• 5-8年: 70分</div>
                                        <div>• 8-10年: 60分</div>
                                        <div>• 超过10年: 每年递减3分</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 维修频率详情 -->
                        <div v-if="healthDetail.dimensions && healthDetail.dimensions.repairFrequency" class="bg-white p-6 rounded-lg border border-gray-200">
                            <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <span class="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                                维修频率评分详情 (权重: 30%)
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <div class="text-sm text-gray-600 mb-2">统计数据</div>
                                    <div class="space-y-1 text-sm">
                                        <div>年均维修率: <span class="font-medium">{{ healthDetail.dimensions.repairFrequency.details?.annualRepairRate || 'N/A' }} 次/年</span></div>
                                        <div>总维修次数: <span class="font-medium">{{ healthDetail.dimensions.repairFrequency.details?.totalRepairs || 'N/A' }} 次</span></div>
                                        <div>频率分类: <span class="font-medium">{{ healthDetail.dimensions.repairFrequency.details?.category || 'N/A' }}</span></div>
                                        <div>评分: <span class="font-medium text-green-600">{{ healthDetail.dimensions.repairFrequency.score }} 分</span></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-600 mb-2">评分标准</div>
                                    <div class="text-xs text-gray-500 space-y-1">
                                        <div>• 无维修记录: 100分</div>
                                        <div>• ≤0.5次/年: 90分</div>
                                        <div>• 0.5-1次/年: 80分</div>
                                        <div>• 1-2次/年: 70分</div>
                                        <div>• 2-3次/年: 60分</div>
                                        <div>• >3次/年: 按比例递减</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 故障严重程度详情 -->
                        <div v-if="healthDetail.dimensions && healthDetail.dimensions.faultSeverity" class="bg-white p-6 rounded-lg border border-gray-200">
                            <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <span class="w-3 h-3 bg-orange-500 rounded-full mr-2"></span>
                                故障严重程度评分详情 (权重: 30%)
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <div class="text-sm text-gray-600 mb-2">故障分布</div>
                                    <div class="space-y-1 text-sm">
                                        <div>严重故障: <span class="font-medium text-red-600">{{ healthDetail.dimensions.faultSeverity.details?.severeCount || 0 }} 次</span></div>
                                        <div>一般故障: <span class="font-medium text-orange-600">{{ healthDetail.dimensions.faultSeverity.details?.moderateCount || 0 }} 次</span></div>
                                        <div>轻微故障: <span class="font-medium text-yellow-600">{{ healthDetail.dimensions.faultSeverity.details?.minorCount || 0 }} 次</span></div>
                                        <div>加权扣分: <span class="font-medium">{{ healthDetail.dimensions.faultSeverity.details?.weightedDeduction || 0 }} 分</span></div>
                                        <div>评分: <span class="font-medium text-orange-600">{{ healthDetail.dimensions.faultSeverity.score }} 分</span></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-600 mb-2">扣分标准</div>
                                    <div class="text-xs text-gray-500 space-y-1">
                                        <div>• 严重故障: 扣50分</div>
                                        <div>• 一般故障: 扣25分</div>
                                        <div>• 轻微故障: 扣10分</div>
                                        <div>• 时间权重: 越近影响越大</div>
                                        <div>• 3个月内: 100%权重</div>
                                        <div>• 6个月内: 80%权重</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 保养情况详情 -->
                        <div v-if="healthDetail.dimensions && healthDetail.dimensions.maintenance" class="bg-white p-6 rounded-lg border border-gray-200">
                            <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <span class="w-3 h-3 bg-purple-500 rounded-full mr-2"></span>
                                保养情况评分详情 (权重: 20%)
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <div class="text-sm text-gray-600 mb-2">保养统计</div>
                                    <div class="space-y-1 text-sm">
                                        <div>保养比例: <span class="font-medium">{{ Math.round((healthDetail.dimensions.maintenance.details?.maintenanceRatio || 0) * 100) }}%</span></div>
                                        <div>距上次保养: <span class="font-medium">{{ healthDetail.dimensions.maintenance.details?.daysSinceLastMaintenance || 'N/A' }} 天</span></div>
                                        <div>比例得分: <span class="font-medium">{{ healthDetail.dimensions.maintenance.details?.ratioScore || 'N/A' }} 分</span></div>
                                        <div>时效得分: <span class="font-medium">{{ healthDetail.dimensions.maintenance.details?.recentScore || 'N/A' }} 分</span></div>
                                        <div>评分: <span class="font-medium text-purple-600">{{ healthDetail.dimensions.maintenance.score }} 分</span></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-600 mb-2">评分标准</div>
                                    <div class="text-xs text-gray-500 space-y-1">
                                        <div>• 保养比例得分(60%权重)</div>
                                        <div>• 最近保养时效得分(40%权重)</div>
                                        <div>• ≤30天: 100分</div>
                                        <div>• 31-90天: 80分</div>
                                        <div>• 91-180天: 60分</div>
                                        <div>• >365天: 40分</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据来源标签页 -->
                    <div v-if="activeTab === 'data'" class="space-y-6">
                        <!-- 设备基本信息 -->
                        <div v-if="healthDetail.equipment" class="bg-white p-6 rounded-lg border border-gray-200">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">设备基本信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="space-y-2">
                                    <div class="text-sm"><span class="text-gray-600">设备名称:</span> <span class="font-medium">{{ healthDetail.equipment.name }}</span></div>
                                    <div class="text-sm"><span class="text-gray-600">设备编号:</span> <span class="font-medium">{{ healthDetail.equipment.code }}</span></div>
                                    <div class="text-sm"><span class="text-gray-600">设备型号:</span> <span class="font-medium">{{ healthDetail.equipment.model }}</span></div>
                                    <div class="text-sm"><span class="text-gray-600">制造商:</span> <span class="font-medium">{{ healthDetail.equipment.manufacturer }}</span></div>
                                </div>
                                <div class="space-y-2">
                                    <div class="text-sm"><span class="text-gray-600">制造日期:</span> <span class="font-medium">{{ formatDate(healthDetail.equipment.manufacture_date || healthDetail.equipment.manufactureDate) }}</span></div>
                                    <div class="text-sm"><span class="text-gray-600">安装日期:</span> <span class="font-medium">{{ formatDate(healthDetail.equipment.install_date || healthDetail.equipment.installDate) }}</span></div>
                                    <div class="text-sm"><span class="text-gray-600">所属区域:</span> <span class="font-medium">{{ healthDetail.equipment.area }}</span></div>
                                    <div class="text-sm"><span class="text-gray-600">使用年限:</span> <span class="font-medium">{{ Math.round(((new Date() - new Date(healthDetail.equipment.manufactureDate)) / (365.25 * 24 * 60 * 60 * 1000)) * 10) / 10 }} 年</span></div>
                                </div>
                            </div>
                        </div>

                        <!-- 维修记录统计 -->
                        <div v-if="healthDetail.maintenanceStats" class="bg-white p-6 rounded-lg border border-gray-200">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">维修记录统计</h4>
                            <div class="space-y-4">
                                <!-- 按类型统计 -->
                                <div>
                                    <h5 class="text-sm font-medium text-gray-700 mb-2">按类型统计</h5>
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                        <div v-for="stat in getMaintenanceStatsByType(healthDetail.maintenanceStats)" :key="stat.type"
                                             class="text-center p-3 bg-gray-50 rounded-lg">
                                            <div class="text-lg font-bold" :class="getMaintenanceTypeColor(stat.type)">{{ stat.count }}</div>
                                            <div class="text-xs text-gray-600">{{ getMaintenanceTypeName(stat.type) }}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 按严重程度统计 -->
                                <div>
                                    <h5 class="text-sm font-medium text-gray-700 mb-2">故障严重程度统计</h5>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div v-for="stat in getMaintenanceStatsBySeverity(healthDetail.maintenanceStats)" :key="stat.severity"
                                             class="text-center p-3 bg-gray-50 rounded-lg">
                                            <div class="text-lg font-bold" :class="getSeverityColor(stat.severity)">{{ stat.count }}</div>
                                            <div class="text-xs text-gray-600">{{ getSeverityName(stat.severity) }}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 最近记录 -->
                                <div v-if="healthDetail.maintenanceStats.length > 0">
                                    <h5 class="text-sm font-medium text-gray-700 mb-2">最近记录时间</h5>
                                    <div class="text-sm text-gray-600">
                                        最后维修: {{ getLatestMaintenanceDate(healthDetail.maintenanceStats) }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 计算说明 -->
                        <div class="bg-blue-50 p-6 rounded-lg border border-blue-200">
                            <h4 class="text-lg font-medium text-blue-800 mb-4">健康度计算说明</h4>
                            <div class="space-y-3 text-sm text-blue-700">
                                <div>
                                    <strong>四维度评估体系:</strong> 采用设备年龄、维修频率、故障严重程度、保养情况四个维度综合评估设备健康状况。
                                </div>
                                <div>
                                    <strong>权重分配:</strong> 维修频率和故障严重程度各占30%，设备年龄和保养情况各占20%。
                                </div>
                                <div>
                                    <strong>评分标准:</strong> 每个维度满分100分，根据具体情况按标准扣分，最终加权平均得出总分。
                                </div>
                                <div>
                                    <strong>等级划分:</strong> 90-100分为优秀，80-89分为良好，70-79分为一般，60-69分为较差，0-59分为危险。
                                </div>
                                <div>
                                    <strong>数据来源:</strong> 基于设备基本信息、历史维修记录、保养记录等真实数据计算得出。
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 维护建议 -->
                    <div v-if="healthDetail.recommendations && healthDetail.recommendations.length > 0">
                        <h4 class="text-lg font-medium text-gray-900 mb-3">维护建议</h4>
                        <div class="space-y-2">
                            <div v-for="(recommendation, index) in healthDetail.recommendations" :key="index"
                                 class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-yellow-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div class="text-sm text-gray-700">{{ recommendation }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                        <button @click="handleCalculateHealth($event, selectedEquipment.id)"
                                :disabled="isCalculating(selectedEquipment.id)"
                                type="button"
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span v-if="isCalculating(selectedEquipment.id)" class="inline-flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                计算中...
                            </span>
                            <span v-else>重新计算健康度</span>
                        </button>
                        <button @click="showDetailModal = false"
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 评估标准说明模态框 -->
        <div v-if="showStandardModal" class="modal-container fixed inset-0 z-50 flex items-center justify-center p-4">
            <div class="modal-overlay fixed inset-0" @click="showStandardModal = false"></div>
            <div class="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <!-- 模态框头部 -->
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-semibold text-gray-800">设备健康度评估标准说明</h3>
                    <button @click="showStandardModal = false" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- 评估概述 -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">评估概述</h4>
                    <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <p class="text-gray-700 mb-2">设备健康度评估采用四维度综合评分算法，总分100分：</p>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3">
                            <div class="text-center">
                                <div class="text-blue-600 font-semibold text-lg">20%</div>
                                <div class="text-sm text-gray-600">设备年龄</div>
                            </div>
                            <div class="text-center">
                                <div class="text-green-600 font-semibold text-lg">30%</div>
                                <div class="text-sm text-gray-600">维修频率</div>
                            </div>
                            <div class="text-center">
                                <div class="text-orange-600 font-semibold text-lg">30%</div>
                                <div class="text-sm text-gray-600">故障严重程度</div>
                            </div>
                            <div class="text-center">
                                <div class="text-purple-600 font-semibold text-lg">20%</div>
                                <div class="text-sm text-gray-600">保养情况</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 健康度等级划分 -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">健康度等级划分</h4>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                            <div class="text-green-600 font-semibold text-lg">优秀</div>
                            <div class="text-sm text-gray-600 mt-1">90-100分</div>
                            <div class="text-xs text-gray-500 mt-2">设备状态极佳，维护良好</div>
                        </div>
                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <div class="text-blue-600 font-semibold text-lg">良好</div>
                            <div class="text-sm text-gray-600 mt-1">80-89分</div>
                            <div class="text-xs text-gray-500 mt-2">设备状态良好，正常运行</div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                            <div class="text-yellow-600 font-semibold text-lg">一般</div>
                            <div class="text-sm text-gray-600 mt-1">70-79分</div>
                            <div class="text-xs text-gray-500 mt-2">设备状态一般，需要关注</div>
                        </div>
                        <div class="bg-orange-50 p-4 rounded-lg border border-orange-200">
                            <div class="text-orange-600 font-semibold text-lg">较差</div>
                            <div class="text-sm text-gray-600 mt-1">60-69分</div>
                            <div class="text-xs text-gray-500 mt-2">设备状态较差，需要维护</div>
                        </div>
                        <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                            <div class="text-red-600 font-semibold text-lg">危险</div>
                            <div class="text-sm text-gray-600 mt-1">0-59分</div>
                            <div class="text-xs text-gray-500 mt-2">设备状态危险，急需检修</div>
                        </div>
                    </div>
                </div>

                <!-- 四维度详细算法 -->
                <div class="space-y-6">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">四维度评分算法详解</h4>

                    <!-- 1. 设备年龄评分 -->
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h5 class="text-md font-semibold text-blue-600 mb-3">1. 设备年龄评分 (权重: 20%)</h5>
                        <p class="text-gray-700 mb-4">基于设备制造日期计算使用年限，评估设备老化程度对健康度的影响。</p>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h6 class="font-medium text-gray-800 mb-2">评分标准：</h6>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">不足1年：</span>
                                    <span class="font-medium text-green-600">100分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">1-3年：</span>
                                    <span class="font-medium text-green-600">90分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">3-5年：</span>
                                    <span class="font-medium text-blue-600">80分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">5-8年：</span>
                                    <span class="font-medium text-yellow-600">70分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">8-10年：</span>
                                    <span class="font-medium text-orange-600">60分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">超过10年：</span>
                                    <span class="font-medium text-red-600">60分起，每年递减3分</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 2. 维修频率评分 -->
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h5 class="text-md font-semibold text-green-600 mb-3">2. 维修频率评分 (权重: 30%)</h5>
                        <p class="text-gray-700 mb-4">统计设备年均维修次数，评估设备可靠性。仅统计维修记录，不包括保养记录。</p>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h6 class="font-medium text-gray-800 mb-2">评分标准：</h6>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">无维修记录：</span>
                                    <span class="font-medium text-green-600">100分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">< 0.5次/年：</span>
                                    <span class="font-medium text-green-600">100分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">0.5-1次/年：</span>
                                    <span class="font-medium text-green-600">90分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">1-2次/年：</span>
                                    <span class="font-medium text-blue-600">80分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">2-3次/年：</span>
                                    <span class="font-medium text-yellow-600">70分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">3-4次/年：</span>
                                    <span class="font-medium text-orange-600">60分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">> 4次/年：</span>
                                    <span class="font-medium text-red-600">60分起，每次递减10分</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 3. 故障严重程度评分 -->
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h5 class="text-md font-semibold text-orange-600 mb-3">3. 故障严重程度评分 (权重: 30%)</h5>
                        <p class="text-gray-700 mb-4">基于历史故障记录的严重程度和时间权重，评估设备故障风险。</p>
                        <div class="bg-gray-50 p-4 rounded-lg mb-4">
                            <h6 class="font-medium text-gray-800 mb-2">严重程度扣分：</h6>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">严重故障：</span>
                                    <span class="font-medium text-red-600">扣50分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">一般故障：</span>
                                    <span class="font-medium text-orange-600">扣25分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">轻微故障：</span>
                                    <span class="font-medium text-yellow-600">扣10分</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h6 class="font-medium text-gray-800 mb-2">时间权重系数：</h6>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">3个月内：</span>
                                    <span class="font-medium">权重 1.0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">3-6个月：</span>
                                    <span class="font-medium">权重 0.8</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">6-12个月：</span>
                                    <span class="font-medium">权重 0.6</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">1-2年：</span>
                                    <span class="font-medium">权重 0.4</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">2年以上：</span>
                                    <span class="font-medium">权重 0.2</span>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">最终得分 = 100 - Σ(严重程度扣分 × 时间权重)，最低15分</p>
                        </div>
                    </div>

                    <!-- 4. 保养情况评分 -->
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h5 class="text-md font-semibold text-purple-600 mb-3">4. 保养情况评分 (权重: 20%)</h5>
                        <p class="text-gray-700 mb-4">综合评估设备保养比例和最近保养时间，反映设备维护水平。</p>
                        <div class="bg-gray-50 p-4 rounded-lg mb-4">
                            <h6 class="font-medium text-gray-800 mb-2">保养比例得分 (60%权重)：</h6>
                            <div class="text-sm space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">无任何记录：</span>
                                    <span class="font-medium text-green-600">85分 (设备状态良好)</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">只有保养记录：</span>
                                    <span class="font-medium text-green-600">100分 (维护很好)</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">有维修和保养：</span>
                                    <span class="font-medium">保养比例 × 100分</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h6 class="font-medium text-gray-800 mb-2">最近保养时间得分 (40%权重)：</h6>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">30天内：</span>
                                    <span class="font-medium text-green-600">100分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">30-60天：</span>
                                    <span class="font-medium text-green-600">90分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">60-90天：</span>
                                    <span class="font-medium text-blue-600">80分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">90-180天：</span>
                                    <span class="font-medium text-yellow-600">70分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">180-365天：</span>
                                    <span class="font-medium text-orange-600">60分</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">超过365天：</span>
                                    <span class="font-medium text-red-600">40分</span>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">最终得分 = 保养比例得分 × 0.6 + 最近保养得分 × 0.4</p>
                        </div>
                    </div>
                </div>

                <!-- 总分计算公式 -->
                <div class="mt-8 bg-blue-50 p-6 rounded-lg border border-blue-200">
                    <h4 class="text-lg font-semibold text-blue-800 mb-4">总分计算公式</h4>
                    <div class="bg-white p-4 rounded-lg border border-blue-100">
                        <p class="text-center text-lg font-mono text-gray-800">
                            <strong>总健康度得分 = </strong><br class="md:hidden">
                            <span class="text-blue-600">设备年龄得分 × 0.2</span> +
                            <span class="text-green-600">维修频率得分 × 0.3</span> + <br class="md:hidden">
                            <span class="text-orange-600">故障严重程度得分 × 0.3</span> +
                            <span class="text-purple-600">保养情况得分 × 0.2</span>
                        </p>
                    </div>
                    <div class="mt-4 text-sm text-gray-600">
                        <p><strong>说明：</strong></p>
                        <ul class="list-disc list-inside space-y-1 mt-2">
                            <li>总分范围：0-100分，分数越高表示设备健康状况越好</li>
                            <li>系统会根据设备的实际使用情况和维护记录自动计算健康度</li>
                            <li>建议定期更新设备维护记录以获得准确的健康度评估</li>
                            <li>对于健康度较低的设备，建议及时进行检修和保养</li>
                        </ul>
                    </div>
                </div>

                <!-- 关闭按钮 -->
                <div class="flex justify-end mt-6 pt-4 border-t border-gray-200">
                    <button @click="showStandardModal = false"
                            class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script src="/js/libs/chart.umd.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/equipment/health.js"></script>
</body>
</html>
