/**
 * 数据库自动初始化脚本
 * 在新服务器环境中自动创建所有必需的数据库表
 */

const { Pool } = require('pg');
const logger = require('../utils/logger');
require('dotenv').config();

class DatabaseInitializer {
    constructor() {
        this.pool = new Pool({
            host: process.env.POSTGRES_HOST || 'localhost',
            port: process.env.POSTGRES_PORT || 5432,
            user: process.env.POSTGRES_USER || 'postgres',
            password: process.env.POSTGRES_PASSWORD,
            database: process.env.POSTGRES_DATABASE || 'makrite_system',
            max: parseInt(process.env.POSTGRES_POOL_MAX) || 20,
            min: parseInt(process.env.POSTGRES_POOL_MIN) || 2,
            idleTimeoutMillis: parseInt(process.env.POSTGRES_POOL_IDLE_TIMEOUT) || 30000,
            connectionTimeoutMillis: parseInt(process.env.POSTGRES_POOL_CONNECTION_TIMEOUT) || 2000,
        });
    }

    /**
     * 初始化所有数据库表
     */
    async initializeAllTables() {
        try {
            logger.info('开始数据库表初始化...');
            
            // 检查数据库连接
            await this.testConnection();
            
            // 创建所有表
            await this.createUsersTable();
            await this.createUserSessionsTable();
            await this.createUserLoginLogsTable();
            await this.createDepartmentsTable();
            await this.createApplicationsTable();
            await this.createApplicationAttachmentsTable();
            await this.createProductsTable();
            await this.createCapacityTable();
            await this.createSchedulesTable();
            await this.createEquipmentTable();
            await this.createEquipmentHealthTable();
            await this.createEquipmentHealthHistoryTable();
            await this.createMaintenanceTable();
            await this.createEquipmentMaintenanceTable();
            await this.createQualityTable();
            await this.createQualityReportsTable();
            await this.createFileManagementTable();
            await this.createPermissionTemplatesTable();
            await this.createPerformanceLogsTable();
            await this.createWarehouseTables();
            
            logger.info('数据库表初始化完成！');
            return true;
        } catch (error) {
            logger.error('数据库表初始化失败:', error);
            throw error;
        }
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        try {
            const client = await this.pool.connect();
            await client.query('SELECT NOW()');
            client.release();
            logger.info('数据库连接测试成功');
        } catch (error) {
            logger.error('数据库连接失败:', error);
            throw new Error('无法连接到PostgreSQL数据库');
        }
    }

    /**
     * 创建用户表
     */
    async createUsersTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS users (
                id VARCHAR(50) PRIMARY KEY,
                usercode VARCHAR(50) UNIQUE,
                username VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE,
                role VARCHAR(50) NOT NULL DEFAULT 'user',
                department VARCHAR(100),
                permissions JSONB DEFAULT '[]',
                active BOOLEAN DEFAULT true,
                has_signature BOOLEAN DEFAULT false,
                signature_path VARCHAR(500),
                signature_base64 TEXT,
                last_login_at TIMESTAMP,
                last_active_at TIMESTAMP,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            );
        `;
        await this.executeSQL('users', sql);
    }

    /**
     * 创建用户会话表
     */
    async createUserSessionsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS user_sessions (
                id VARCHAR(50) PRIMARY KEY,
                user_id VARCHAR(50) NOT NULL,
                username VARCHAR(100) NOT NULL,
                session_token VARCHAR(255) UNIQUE NOT NULL,
                ip_address INET,
                user_agent TEXT,
                login_time TIMESTAMP NOT NULL DEFAULT NOW(),
                last_activity TIMESTAMP NOT NULL DEFAULT NOW(),
                expires_at TIMESTAMP NOT NULL,
                active BOOLEAN DEFAULT true,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            );
        `;
        await this.executeSQL('user_sessions', sql);
    }

    /**
     * 创建用户登录日志表
     */
    async createUserLoginLogsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS user_login_logs (
                id VARCHAR(50) PRIMARY KEY,
                user_id VARCHAR(50),
                username VARCHAR(100) NOT NULL,
                ip_address INET,
                user_agent TEXT,
                login_time TIMESTAMP NOT NULL DEFAULT NOW(),
                logout_time TIMESTAMP,
                login_status VARCHAR(20) DEFAULT 'success',
                failure_reason TEXT,
                session_id VARCHAR(100),
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
            );
        `;
        await this.executeSQL('user_login_logs', sql);
    }

    /**
     * 创建部门表
     */
    async createDepartmentsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS departments (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                manager_id VARCHAR(50),
                parent_id VARCHAR(50),
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            );
        `;
        await this.executeSQL('departments', sql);
    }

    /**
     * 创建申请表
     */
    async createApplicationsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS applications (
                id VARCHAR(50) PRIMARY KEY,
                user_id VARCHAR(50),
                applicant VARCHAR(100) NOT NULL,
                department VARCHAR(100) NOT NULL,
                type VARCHAR(50) NOT NULL,
                title VARCHAR(200) NOT NULL,
                description TEXT NOT NULL,
                urgency VARCHAR(20) NOT NULL DEFAULT 'normal',
                quantity INTEGER DEFAULT 1,
                expected_date DATE,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                current_stage VARCHAR(50) DEFAULT 'factory_manager',
                selected_factory_managers JSONB DEFAULT '[]',
                selected_managers JSONB DEFAULT '[]',
                need_manager_approval BOOLEAN DEFAULT false,
                need_ceo_approval BOOLEAN DEFAULT true,
                approval_history JSONB DEFAULT '[]',
                attachments JSONB DEFAULT '[]',
                notes TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
            );
        `;
        await this.executeSQL('applications', sql);
    }

    /**
     * 创建申请附件表
     */
    async createApplicationAttachmentsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS application_attachments (
                id VARCHAR(50) PRIMARY KEY,
                application_id VARCHAR(50) NOT NULL,
                name VARCHAR(255) NOT NULL,
                path TEXT NOT NULL,
                filename VARCHAR(255),
                type VARCHAR(100),
                size INTEGER,
                uploaded_at TIMESTAMP NOT NULL DEFAULT NOW(),
                FOREIGN KEY (application_id) REFERENCES applications (id) ON DELETE CASCADE
            );
        `;
        await this.executeSQL('application_attachments', sql);
    }

    /**
     * 创建产品表
     */
    async createProductsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS products (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                model VARCHAR(100),
                category VARCHAR(100),
                specifications JSONB DEFAULT '{}',
                unit VARCHAR(20) DEFAULT 'pcs',
                standard_time DECIMAL(10,2) DEFAULT 0,
                difficulty_level INTEGER DEFAULT 1,
                quality_requirements JSONB DEFAULT '{}',
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            );
        `;
        await this.executeSQL('products', sql);
    }

    /**
     * 创建产能表
     */
    async createCapacityTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS capacity (
                id VARCHAR(50) PRIMARY KEY,
                date DATE NOT NULL,
                department_id VARCHAR(50) NOT NULL,
                shift VARCHAR(20) NOT NULL,
                available_hours DECIMAL(8,2) NOT NULL DEFAULT 0,
                planned_hours DECIMAL(8,2) NOT NULL DEFAULT 0,
                actual_hours DECIMAL(8,2) DEFAULT 0,
                efficiency_rate DECIMAL(5,2) DEFAULT 0,
                utilization_rate DECIMAL(5,2) DEFAULT 0,
                worker_count INTEGER DEFAULT 0,
                equipment_count INTEGER DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
                UNIQUE(date, department_id, shift)
            );
        `;
        await this.executeSQL('capacity', sql);
    }

    /**
     * 创建排程表
     */
    async createSchedulesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS schedules (
                id VARCHAR(50) PRIMARY KEY,
                application_id VARCHAR(50),
                product_id VARCHAR(50),
                department_id VARCHAR(50),
                title VARCHAR(200) NOT NULL,
                description TEXT,
                planned_start_date DATE NOT NULL,
                planned_end_date DATE NOT NULL,
                actual_start_date DATE,
                actual_end_date DATE,
                planned_quantity INTEGER DEFAULT 1,
                actual_quantity INTEGER DEFAULT 0,
                priority INTEGER DEFAULT 5,
                status VARCHAR(20) NOT NULL DEFAULT 'planned',
                assigned_workers JSONB DEFAULT '[]',
                assigned_equipment JSONB DEFAULT '[]',
                progress_percentage DECIMAL(5,2) DEFAULT 0,
                quality_status VARCHAR(20) DEFAULT 'pending',
                notes TEXT,
                created_by VARCHAR(50),
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
                FOREIGN KEY (application_id) REFERENCES applications (id) ON DELETE SET NULL,
                FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE SET NULL
            );
        `;
        await this.executeSQL('schedules', sql);
    }

    /**
     * 创建设备表
     */
    async createEquipmentTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS equipment (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                model VARCHAR(100),
                manufacturer VARCHAR(100),
                serial_number VARCHAR(100) UNIQUE,
                department_id VARCHAR(50),
                category VARCHAR(100),
                purchase_date DATE,
                warranty_end_date DATE,
                status VARCHAR(20) NOT NULL DEFAULT 'active',
                location VARCHAR(200),
                specifications JSONB DEFAULT '{}',
                maintenance_interval INTEGER DEFAULT 30,
                last_maintenance_date DATE,
                next_maintenance_date DATE,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            );
        `;
        await this.executeSQL('equipment', sql);
    }

    /**
     * 创建设备健康度表
     */
    async createEquipmentHealthTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS equipment_health (
                id VARCHAR(50) PRIMARY KEY,
                equipment_id VARCHAR(50) NOT NULL,
                total_score DECIMAL(5,2) NOT NULL,
                health_level VARCHAR(20) NOT NULL,
                age_score DECIMAL(5,2),
                repair_frequency_score DECIMAL(5,2),
                fault_severity_score DECIMAL(5,2),
                maintenance_score DECIMAL(5,2),
                assessment_date TIMESTAMP,
                assessor VARCHAR(100),
                calculation_details JSONB DEFAULT '{}',
                recommendations JSONB DEFAULT '[]',
                next_maintenance_date DATE,
                failure_probability DECIMAL(5,4) DEFAULT 0,
                recorded_at TIMESTAMP NOT NULL DEFAULT NOW(),
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            );
        `;
        await this.executeSQL('equipment_health', sql);
    }

    /**
     * 创建设备健康度历史表
     */
    async createEquipmentHealthHistoryTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS equipment_health_history (
                id VARCHAR(50) PRIMARY KEY,
                equipment_id VARCHAR(50) NOT NULL,
                total_score DECIMAL(5,2) NOT NULL,
                health_level VARCHAR(20) NOT NULL,
                age_score DECIMAL(5,2),
                repair_frequency_score DECIMAL(5,2),
                fault_severity_score DECIMAL(5,2),
                maintenance_score DECIMAL(5,2),
                assessment_date TIMESTAMP NOT NULL,
                assessor VARCHAR(100) NOT NULL,
                calculation_details JSONB DEFAULT '{}',
                recommendations JSONB DEFAULT '[]',
                next_maintenance_date DATE,
                failure_probability DECIMAL(5,4) DEFAULT 0,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            );
        `;
        await this.executeSQL('equipment_health_history', sql);
    }

    /**
     * 创建维护表
     */
    async createMaintenanceTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS maintenance (
                id VARCHAR(50) PRIMARY KEY,
                equipment_id VARCHAR(50) NOT NULL,
                maintenance_type VARCHAR(50) NOT NULL,
                scheduled_date DATE NOT NULL,
                actual_date DATE,
                duration_hours DECIMAL(8,2),
                cost DECIMAL(12,2),
                technician VARCHAR(100),
                description TEXT,
                parts_used JSONB DEFAULT '[]',
                status VARCHAR(20) NOT NULL DEFAULT 'scheduled',
                priority VARCHAR(20) DEFAULT 'normal',
                notes TEXT,
                attachments JSONB DEFAULT '[]',
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            );
        `;
        await this.executeSQL('maintenance', sql);
    }

    /**
     * 创建设备维护记录表
     */
    async createEquipmentMaintenanceTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS equipment_maintenance (
                id VARCHAR(50) PRIMARY KEY,
                equipment_id VARCHAR(50) NOT NULL,
                maintenance_type VARCHAR(50) NOT NULL,
                maintenance_date DATE NOT NULL,
                description TEXT,
                cost DECIMAL(12,2),
                technician VARCHAR(100),
                severity_level VARCHAR(20) DEFAULT 'moderate',
                status VARCHAR(20) DEFAULT 'completed',
                duration_hours DECIMAL(8,2),
                parts_used JSONB DEFAULT '[]',
                notes TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            );
        `;
        await this.executeSQL('equipment_maintenance', sql);
    }

    /**
     * 创建质量表
     */
    async createQualityTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS quality (
                id VARCHAR(50) PRIMARY KEY,
                schedule_id VARCHAR(50) NOT NULL,
                product_id VARCHAR(50) NOT NULL,
                inspector VARCHAR(100) NOT NULL,
                inspection_date DATE NOT NULL,
                batch_number VARCHAR(100),
                sample_size INTEGER NOT NULL,
                passed_count INTEGER NOT NULL DEFAULT 0,
                failed_count INTEGER NOT NULL DEFAULT 0,
                defect_types JSONB DEFAULT '[]',
                quality_score DECIMAL(5,2),
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                notes TEXT,
                attachments JSONB DEFAULT '[]',
                corrective_actions JSONB DEFAULT '[]',
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            );
        `;
        await this.executeSQL('quality', sql);
    }

    /**
     * 创建质量报告表
     */
    async createQualityReportsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS quality_reports (
                id VARCHAR(50) PRIMARY KEY,
                title VARCHAR(200) NOT NULL,
                description TEXT,
                report_type VARCHAR(50) DEFAULT 'inspection',
                product_id VARCHAR(50),
                batch_number VARCHAR(100),
                inspector VARCHAR(100) NOT NULL,
                inspection_date DATE NOT NULL,
                sample_size INTEGER DEFAULT 1,
                passed_count INTEGER DEFAULT 0,
                failed_count INTEGER DEFAULT 0,
                defect_types JSONB DEFAULT '[]',
                quality_score DECIMAL(5,2),
                status VARCHAR(20) NOT NULL DEFAULT 'draft',
                file_path VARCHAR(500),
                file_name VARCHAR(255),
                file_size BIGINT,
                uploaded_by VARCHAR(50),
                uploaded_at TIMESTAMP DEFAULT NOW(),
                notes TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
                FOREIGN KEY (uploaded_by) REFERENCES users (id) ON DELETE SET NULL
            );
        `;
        await this.executeSQL('quality_reports', sql);
    }

    /**
     * 创建文件管理表
     */
    async createFileManagementTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS file_management (
                id VARCHAR(50) PRIMARY KEY,
                file_name VARCHAR(255) NOT NULL,
                original_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size BIGINT NOT NULL,
                file_type VARCHAR(100) NOT NULL,
                mime_type VARCHAR(100),
                category VARCHAR(100) NOT NULL,
                description TEXT,
                tags JSONB DEFAULT '[]',
                uploaded_by VARCHAR(50) NOT NULL,
                department_id VARCHAR(50),
                is_public BOOLEAN DEFAULT false,
                download_count INTEGER DEFAULT 0,
                version VARCHAR(20) DEFAULT '1.0',
                status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            );
        `;
        await this.executeSQL('file_management', sql);
    }

    /**
     * 创建权限模板表
     */
    async createPermissionTemplatesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS permission_templates (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                permissions JSONB NOT NULL DEFAULT '[]',
                is_default BOOLEAN DEFAULT false,
                is_active BOOLEAN DEFAULT true,
                created_by VARCHAR(50),
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            );
        `;
        await this.executeSQL('permission_templates', sql);
    }

    /**
     * 创建性能日志表
     */
    async createPerformanceLogsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS performance_logs (
                id SERIAL PRIMARY KEY,
                endpoint VARCHAR(255) NOT NULL,
                method VARCHAR(10) NOT NULL,
                response_time INTEGER NOT NULL,
                status_code INTEGER NOT NULL,
                user_id VARCHAR(50),
                ip_address INET,
                error_message TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT NOW()
            );
        `;
        await this.executeSQL('performance_logs', sql);
    }

    /**
     * 创建仓库管理相关表
     */
    async createWarehouseTables() {
        // 仓库物料表
        const materialsSql = `
            CREATE TABLE IF NOT EXISTS warehouse_materials (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                category VARCHAR(100),
                unit VARCHAR(20) DEFAULT 'pcs',
                current_stock DECIMAL(12,2) DEFAULT 0,
                min_stock DECIMAL(12,2) DEFAULT 0,
                max_stock DECIMAL(12,2) DEFAULT 0,
                unit_price DECIMAL(12,2) DEFAULT 0,
                supplier VARCHAR(200),
                location VARCHAR(100),
                description TEXT,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            );
        `;
        await this.executeSQL('warehouse_materials', materialsSql);

        // 仓库成品表
        const productsSql = `
            CREATE TABLE IF NOT EXISTS warehouse_products (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                model VARCHAR(100),
                category VARCHAR(100),
                current_stock DECIMAL(12,2) DEFAULT 0,
                min_stock DECIMAL(12,2) DEFAULT 0,
                max_stock DECIMAL(12,2) DEFAULT 0,
                unit_price DECIMAL(12,2) DEFAULT 0,
                location VARCHAR(100),
                description TEXT,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            );
        `;
        await this.executeSQL('warehouse_products', productsSql);

        // 仓库事务表
        const transactionsSql = `
            CREATE TABLE IF NOT EXISTS warehouse_transactions (
                id VARCHAR(50) PRIMARY KEY,
                transaction_number VARCHAR(100) UNIQUE NOT NULL,
                transaction_type VARCHAR(20) NOT NULL,
                item_type VARCHAR(20) NOT NULL,
                item_id VARCHAR(50) NOT NULL,
                quantity DECIMAL(12,2) NOT NULL,
                unit_price DECIMAL(12,2) DEFAULT 0,
                total_amount DECIMAL(12,2) DEFAULT 0,
                batch_number VARCHAR(100),
                qrcode VARCHAR(200),
                operator_id VARCHAR(50) NOT NULL,
                notes TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            );
        `;
        await this.executeSQL('warehouse_transactions', transactionsSql);

        // 仓库二维码表
        const qrcodesSql = `
            CREATE TABLE IF NOT EXISTS warehouse_qrcodes (
                id VARCHAR(50) PRIMARY KEY,
                qrcode VARCHAR(200) UNIQUE NOT NULL,
                item_type VARCHAR(20) NOT NULL,
                item_id VARCHAR(50) NOT NULL,
                batch_number VARCHAR(100),
                quantity DECIMAL(12,2) NOT NULL,
                location VARCHAR(100),
                status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            );
        `;
        await this.executeSQL('warehouse_qrcodes', qrcodesSql);
    }

    /**
     * 执行SQL语句
     */
    async executeSQL(tableName, sql) {
        try {
            await this.pool.query(sql);
            logger.info(`表 ${tableName} 创建/检查完成`);
        } catch (error) {
            logger.error(`创建表 ${tableName} 失败:`, error);
            throw error;
        }
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        await this.pool.end();
    }
}

module.exports = DatabaseInitializer;
