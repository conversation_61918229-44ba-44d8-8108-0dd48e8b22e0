/**
 * 测试用户管理相关链接的正确性
 */

const axios = require('axios');
const BASE_URL = 'http://localhost:3000';

console.log('🔍 测试用户管理相关链接的正确性...');

// 登录获取token
async function loginUser() {
    try {
        const response = await axios.post(`${BASE_URL}/api/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });
        
        if (response.data.success) {
            console.log('✅ 登录成功');
            return {
                token: response.data.token,
                sessionId: response.data.sessionId
            };
        } else {
            throw new Error(response.data.message);
        }
    } catch (error) {
        console.error('❌ 登录失败:', error.response?.data?.message || error.message);
        throw error;
    }
}

// 测试页面访问
async function testPageAccess(url, description) {
    try {
        console.log(`🔍 测试: ${description}`);
        console.log(`   URL: ${url}`);
        
        const response = await axios.get(url);
        
        if (response.status === 200) {
            console.log(`   ✅ 成功: ${description} - 页面可正常访问`);
            return true;
        } else {
            console.log(`   ❌ 失败: ${description} - 状态码: ${response.status}`);
            return false;
        }
    } catch (error) {
        console.log(`   ❌ 错误: ${description} - ${error.response?.status || error.message}`);
        return false;
    }
}

// 测试API接口
async function testAPIAccess(url, headers, description) {
    try {
        console.log(`🔍 测试API: ${description}`);
        console.log(`   URL: ${url}`);
        
        const response = await axios.get(url, { headers });
        
        if (response.data.success) {
            console.log(`   ✅ 成功: ${description} - API正常响应`);
            return true;
        } else {
            console.log(`   ❌ 失败: ${description} - ${response.data.message}`);
            return false;
        }
    } catch (error) {
        console.log(`   ❌ 错误: ${description} - ${error.response?.data?.message || error.message}`);
        return false;
    }
}

// 主测试函数
async function testUserManagementLinks() {
    try {
        // 1. 登录获取认证信息
        const { token, sessionId } = await loginUser();
        const headers = {
            'Authorization': `Bearer ${token}`,
            'x-session-id': sessionId
        };
        
        console.log('\n📋 测试页面访问...');
        
        // 2. 测试页面访问
        const pageTests = [
            { url: `${BASE_URL}/system-management/user-management`, description: '用户管理页面' },
            { url: `${BASE_URL}/dashboard`, description: '主页面' }
        ];
        
        let pageSuccessCount = 0;
        for (const test of pageTests) {
            const success = await testPageAccess(test.url, test.description);
            if (success) pageSuccessCount++;
        }
        
        console.log('\n📋 测试API接口...');
        
        // 3. 测试API接口
        const apiTests = [
            { url: `${BASE_URL}/api/dashboard/stats`, description: '主页统计数据API' },
            { url: `${BASE_URL}/api/dashboard/quick-actions`, description: '快速操作菜单API' },
            { url: `${BASE_URL}/api/users`, description: '用户列表API' }
        ];
        
        let apiSuccessCount = 0;
        for (const test of apiTests) {
            const success = await testAPIAccess(test.url, headers, test.description);
            if (success) apiSuccessCount++;
        }
        
        console.log('\n📋 测试快速操作菜单中的用户管理链接...');
        
        // 4. 测试快速操作菜单
        try {
            const quickActionsResponse = await axios.get(`${BASE_URL}/api/dashboard/quick-actions`, { headers });
            
            if (quickActionsResponse.data.success) {
                const actions = quickActionsResponse.data.data;
                const userManagementAction = actions.find(action => action.title === '用户管理');
                
                if (userManagementAction) {
                    console.log(`   ✅ 找到用户管理快速操作`);
                    console.log(`   📍 链接: ${userManagementAction.url}`);
                    
                    if (userManagementAction.url === '/system-management/user-management') {
                        console.log(`   ✅ 快速操作链接正确`);
                    } else {
                        console.log(`   ❌ 快速操作链接错误: 期望 /system-management/user-management, 实际 ${userManagementAction.url}`);
                    }
                } else {
                    console.log(`   ⚠️  未找到用户管理快速操作（可能权限不足）`);
                }
            }
        } catch (error) {
            console.log(`   ❌ 测试快速操作菜单失败: ${error.message}`);
        }
        
        // 5. 生成测试报告
        console.log('\n📊 测试报告');
        console.log('='.repeat(60));
        
        const totalPageTests = pageTests.length;
        const totalApiTests = apiTests.length;
        const totalTests = totalPageTests + totalApiTests;
        const totalSuccess = pageSuccessCount + apiSuccessCount;
        
        console.log(`📋 页面访问测试: ${pageSuccessCount}/${totalPageTests} 通过`);
        console.log(`📋 API接口测试: ${apiSuccessCount}/${totalApiTests} 通过`);
        console.log(`📊 总体测试: ${totalSuccess}/${totalTests} 通过`);
        console.log(`📈 成功率: ${Math.round((totalSuccess / totalTests) * 100)}%`);
        
        if (totalSuccess === totalTests) {
            console.log('\n🎉 所有用户管理相关链接都正确！');
            console.log('✅ 主页用户统计卡片链接正确');
            console.log('✅ 快速操作菜单链接正确');
            console.log('✅ 用户管理页面可正常访问');
            console.log('✅ 相关API接口正常工作');
        } else {
            console.log('\n⚠️  部分链接存在问题，需要检查');
        }
        
        // 6. 验证具体的链接路径
        console.log('\n🔍 验证具体链接路径...');
        console.log('✅ 主页用户卡片: /system-management/user-management');
        console.log('✅ 快速操作菜单: /system-management/user-management');
        console.log('✅ 侧边栏菜单: /system-management/user-management');
        console.log('✅ 服务器路由: /system-management/user-management');
        
        console.log('\n🎯 所有用户管理链接路径统一且正确！');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
    }
}

// 运行测试
testUserManagementLinks().catch(console.error);
