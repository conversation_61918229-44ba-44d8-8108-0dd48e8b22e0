/**
 * 主页数据服务
 * 聚合各模块数据为主页提供统计信息
 */

const applicationService = require('./applicationService');
const userService = require('./userService');
const logger = require('../utils/logger');

// 记录服务器启动时间
const SERVER_START_TIME = Date.now();

// CPU使用率计算相关变量
let lastCpuInfo = null;
let lastCpuTime = null;

class DashboardService {
    /**
     * 计算CPU使用率
     * @returns {Promise<number>} CPU使用率百分比
     */
    async getCpuUsage() {
        return new Promise((resolve) => {
            const os = require('os');
            const cpus = os.cpus();

            // 获取当前CPU信息
            let currentIdle = 0;
            let currentTotal = 0;

            cpus.forEach(cpu => {
                for (let type in cpu.times) {
                    currentTotal += cpu.times[type];
                }
                currentIdle += cpu.times.idle;
            });

            const currentTime = Date.now();

            if (lastCpuInfo && lastCpuTime) {
                // 计算时间差和CPU时间差
                const timeDiff = currentTime - lastCpuTime;
                const idleDiff = currentIdle - lastCpuInfo.idle;
                const totalDiff = currentTotal - lastCpuInfo.total;

                if (totalDiff > 0) {
                    const cpuUsage = Math.round(100 - (idleDiff / totalDiff) * 100);

                    // 更新上次记录
                    lastCpuInfo = { idle: currentIdle, total: currentTotal };
                    lastCpuTime = currentTime;

                    resolve(Math.max(0, Math.min(100, cpuUsage))); // 确保在0-100范围内
                    return;
                }
            }

            // 首次调用或计算失败时，等待一段时间后再次计算
            setTimeout(() => {
                lastCpuInfo = { idle: currentIdle, total: currentTotal };
                lastCpuTime = currentTime;

                // 再次获取CPU信息
                let newIdle = 0;
                let newTotal = 0;

                os.cpus().forEach(cpu => {
                    for (let type in cpu.times) {
                        newTotal += cpu.times[type];
                    }
                    newIdle += cpu.times.idle;
                });

                const idleDiff = newIdle - currentIdle;
                const totalDiff = newTotal - currentTotal;

                if (totalDiff > 0) {
                    const cpuUsage = Math.round(100 - (idleDiff / totalDiff) * 100);
                    resolve(Math.max(0, Math.min(100, cpuUsage)));
                } else {
                    resolve(0);
                }
            }, 100); // 等待100ms
        });
    }
    /**
     * 获取主页统计数据
     * @param {Object} user - 当前用户信息
     * @returns {Promise<Object>} 统计数据
     */
    async getDashboardStats(user) {
        try {
            const stats = {
                applications: await this.getApplicationStats(user),
                equipment: await this.getEquipmentStats(user),
                quality: await this.getQualityStats(user),
                schedules: await this.getScheduleStats(user),
                users: await this.getUserStats(user),
                system: await this.getSystemStats()
            };

            return {
                success: true,
                data: stats
            };
        } catch (error) {
            logger.error('获取主页统计数据失败:', error);
            throw error;
        }
    }

    /**
     * 获取申请管理统计
     */
    async getApplicationStats(user) {
        try {
            const { databaseAdapter } = require('../database/databaseAdapter');
            const applicationRepository = databaseAdapter.getApplicationRepository();
            const now = new Date();
            const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

            // 基础统计
            const totalResult = await applicationRepository.query(`SELECT COUNT(*) as count FROM applications`);
            const total = parseInt(totalResult.rows[0].count) || 0;

            const pendingResult = await applicationRepository.query(`SELECT COUNT(*) as count FROM applications WHERE status = 'pending'`);
            const pending = parseInt(pendingResult.rows[0].count) || 0;

            const thisMonthResult = await applicationRepository.query(`SELECT COUNT(*) as count FROM applications WHERE created_at >= $1`, [thisMonth.toISOString()]);
            const thisMonth_count = parseInt(thisMonthResult.rows[0].count) || 0;

            const approvedResult = await applicationRepository.query(`SELECT COUNT(*) as count FROM applications WHERE status = 'approved'`);
            const approved = parseInt(approvedResult.rows[0].count) || 0;

            // 计算通过率
            const approvalRate = total > 0 ? Math.round((approved / total) * 100) : 0;

            // 用户相关统计
            let myApplications = 0;
            let myPendingApprovals = 0;

            if (user) {
                const myAppsResult = await applicationRepository.query(`SELECT COUNT(*) as count FROM applications WHERE applicant = $1`, [user.username]);
                myApplications = parseInt(myAppsResult.rows[0].count) || 0;

                // 获取待我审批的申请数量
                const canApprove = this.canUserApprove(user);
                if (canApprove) {
                    myPendingApprovals = await this.getMyPendingApprovalsCount(user);
                }
            }

            return {
                total,
                pending,
                thisMonth: thisMonth_count,
                approvalRate,
                myApplications,
                myPendingApprovals
            };
        } catch (error) {
            logger.error('获取申请统计失败:', error);
            return {
                total: 0,
                pending: 0,
                thisMonth: 0,
                approvalRate: 0,
                myApplications: 0,
                myPendingApprovals: 0
            };
        }
    }

    /**
     * 获取设备管理统计
     */
    async getEquipmentStats(user) {
        try {
            const { databaseAdapter } = require('../database/databaseAdapter');
            const equipmentRepository = databaseAdapter.getEquipmentRepository();

            const totalResult = await equipmentRepository.query(`SELECT COUNT(*) as count FROM equipment`);
            const total = parseInt(totalResult.rows[0].count) || 0;

            const activeResult = await equipmentRepository.query(`SELECT COUNT(*) as count FROM equipment WHERE status = 'active'`);
            const active = parseInt(activeResult.rows[0].count) || 0;

            const maintenanceResult = await equipmentRepository.query(`SELECT COUNT(*) as count FROM equipment WHERE status = 'maintenance'`);
            const maintenance = parseInt(maintenanceResult.rows[0].count) || 0;

            const inactiveResult = await equipmentRepository.query(`SELECT COUNT(*) as count FROM equipment WHERE status = 'inactive'`);
            const inactive = parseInt(inactiveResult.rows[0].count) || 0;

            // 健康度统计（如果有健康度数据）
            let healthyCount = 0;
            let warningCount = 0;
            try {
                const healthResult = await equipmentRepository.query(`SELECT COUNT(*) as count FROM equipment_health WHERE health_score >= 80`);
                healthyCount = parseInt(healthResult.rows[0].count) || 0;

                const warningResult = await equipmentRepository.query(`SELECT COUNT(*) as count FROM equipment_health WHERE health_score < 70`);
                warningCount = parseInt(warningResult.rows[0].count) || 0;
            } catch (e) {
                // 健康度表可能不存在，忽略错误
            }

            return {
                total,
                active,
                maintenance,
                inactive,
                healthy: healthyCount,
                warning: warningCount,
                healthRate: total > 0 ? Math.round((active / total) * 100) : 0
            };
        } catch (error) {
            logger.error('获取设备统计失败:', error);
            return {
                total: 0,
                active: 0,
                maintenance: 0,
                inactive: 0,
                healthy: 0,
                warning: 0,
                healthRate: 0
            };
        }
    }

    /**
     * 获取质量管理统计
     */
    async getQualityStats(user) {
        try {
            const { databaseAdapter } = require('../database/databaseAdapter');
            const qualityRepository = databaseAdapter.getQualityRepository();
            const now = new Date();
            const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

            const totalResult = await qualityRepository.query(`SELECT COUNT(*) as count FROM quality_reports`);
            const total = parseInt(totalResult.rows[0].count) || 0;

            const thisMonthResult = await qualityRepository.query(`SELECT COUNT(*) as count FROM quality_reports WHERE created_at >= $1`, [thisMonth.toISOString()]);
            const thisMonth_count = parseInt(thisMonthResult.rows[0].count) || 0;

            const passedResult = await qualityRepository.query(`SELECT COUNT(*) as count FROM quality_reports WHERE conclusion ILIKE '%合格%' OR conclusion ILIKE '%通过%'`);
            const passed = parseInt(passedResult.rows[0].count) || 0;

            const pendingResult = await qualityRepository.query(`SELECT COUNT(*) as count FROM quality_reports WHERE status = 'pending'`);
            const pending = parseInt(pendingResult.rows[0].count) || 0;

            const passRate = total > 0 ? Math.round((passed / total) * 100) : 0;

            return {
                total,
                thisMonth: thisMonth_count,
                passed,
                pending,
                passRate
            };
        } catch (error) {
            logger.error('获取质量统计失败:', error);
            return {
                total: 0,
                thisMonth: 0,
                passed: 0,
                pending: 0,
                passRate: 0
            };
        }
    }

    /**
     * 获取排程管理统计
     */
    async getScheduleStats(user) {
        try {
            const { databaseAdapter } = require('../database/databaseAdapter');
            const scheduleRepository = databaseAdapter.getScheduleRepository();

            const totalResult = await scheduleRepository.query(`SELECT COUNT(*) as count FROM schedules`);
            const total = parseInt(totalResult.rows[0].count) || 0;

            const plannedResult = await scheduleRepository.query(`SELECT COUNT(*) as count FROM schedules WHERE status = 'planned'`);
            const planned = parseInt(plannedResult.rows[0].count) || 0;

            const inProgressResult = await scheduleRepository.query(`SELECT COUNT(*) as count FROM schedules WHERE status = 'in_progress'`);
            const inProgress = parseInt(inProgressResult.rows[0].count) || 0;

            const completedResult = await scheduleRepository.query(`SELECT COUNT(*) as count FROM schedules WHERE status = 'completed'`);
            const completed = parseInt(completedResult.rows[0].count) || 0;

            const delayedResult = await scheduleRepository.query(`SELECT COUNT(*) as count FROM schedules WHERE status = 'delayed'`);
            const delayed = parseInt(delayedResult.rows[0].count) || 0;

            const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

            return {
                total,
                planned,
                inProgress,
                completed,
                delayed,
                completionRate
            };
        } catch (error) {
            logger.error('获取排程统计失败:', error);
            return {
                total: 0,
                planned: 0,
                inProgress: 0,
                completed: 0,
                delayed: 0,
                completionRate: 0
            };
        }
    }

    /**
     * 获取用户统计
     */
    async getUserStats(user) {
        try {
            const { databaseAdapter } = require('../database/databaseAdapter');
            const userRepository = databaseAdapter.getUserRepository();

            const totalResult = await userRepository.query(`SELECT COUNT(*) as count FROM users`);
            const total = parseInt(totalResult.rows[0].count) || 0;

            const activeResult = await userRepository.query(`SELECT COUNT(*) as count FROM users WHERE active = true`);
            const active = parseInt(activeResult.rows[0].count) || 0;

            // 获取今日登录用户数（基于真实登录记录）
            let todayLogin = 0;
            try {
                // 检查是否存在登录日志表
                const tableExistsResult = await userRepository.query(`
                    SELECT table_name FROM information_schema.tables
                    WHERE table_schema = 'public' AND table_name = 'user_login_logs'
                `);

                if (tableExistsResult.rows.length > 0) {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    const todayLoginResult = await userRepository.query(`
                        SELECT COUNT(DISTINCT username) as count
                        FROM user_login_logs
                        WHERE login_time >= $1 AND login_status = 'success'
                    `, [today.toISOString()]);
                    todayLogin = parseInt(todayLoginResult.rows[0].count) || 0;
                }
            } catch (e) {
                // 如果登录日志表不存在，返回0
                todayLogin = 0;
            }

            // 获取在线用户数（基于活跃会话）
            let onlineUsers = 0;
            try {
                // 检查是否存在会话表
                const sessionTableResult = await userRepository.query(`
                    SELECT table_name FROM information_schema.tables
                    WHERE table_schema = 'public' AND table_name = 'user_sessions'
                `);

                if (sessionTableResult.rows.length > 0) {
                    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
                    const onlineResult = await userRepository.query(`
                        SELECT COUNT(DISTINCT user_id) as count
                        FROM user_sessions
                        WHERE last_activity >= $1 AND active = true
                    `, [thirtyMinutesAgo.toISOString()]);
                    onlineUsers = parseInt(onlineResult.rows[0].count) || 0;
                }
            } catch (e) {
                // 如果会话表不存在，返回0
                onlineUsers = 0;
            }

            return {
                total,
                active,
                todayLogin,
                onlineUsers
            };
        } catch (error) {
            logger.error('获取用户统计失败:', error);
            return {
                total: 0,
                active: 0,
                todayLogin: 0,
                onlineUsers: 0
            };
        }
    }

    /**
     * 获取系统统计
     */
    async getSystemStats() {
        try {
            const memoryUsage = process.memoryUsage();
            const uptime = process.uptime();

            // 获取真实的内存使用率
            const memoryUsagePercent = Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100);

            // 获取真实的CPU使用率
            const cpuUsage = await this.getCpuUsage();

            // 获取真实的响应时间（基于最近的请求）
            let responseTime = 0;
            try {
                // 检查是否存在性能监控表
                const { postgresConnectionPool } = require('../utils/postgresConnectionPool');

                const perfTableResult = await postgresConnectionPool.query(`
                    SELECT table_name FROM information_schema.tables
                    WHERE table_schema = 'public' AND table_name = 'performance_logs'
                `);

                if (perfTableResult.rows.length > 0) {
                    const avgResponseResult = await postgresConnectionPool.query(`
                        SELECT AVG(response_time) as avg_time
                        FROM performance_logs
                        WHERE created_at >= NOW() - INTERVAL '1 hour'
                    `);
                    responseTime = avgResponseResult.rows[0].avg_time ? Math.round(avgResponseResult.rows[0].avg_time) : 0;
                }
            } catch (e) {
                // 如果性能监控表不存在，返回0
                responseTime = 0;
            }

            // 格式化运行时间为 "Xh Ym Zs" 格式
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = Math.floor(uptime % 60);

            let uptimeFormatted = '';
            if (hours > 0) {
                uptimeFormatted += `${hours}h `;
            }
            if (minutes > 0 || hours > 0) {
                uptimeFormatted += `${minutes}m `;
            }
            uptimeFormatted += `${seconds}s`;

            return {
                uptime: uptimeFormatted.trim(), // 精确到秒的运行时间
                uptimeSeconds: Math.floor(uptime), // 运行时间秒数，用于前端实时计算
                serverStartTime: SERVER_START_TIME, // 服务器真正的启动时间戳
                memoryUsage: memoryUsagePercent,
                cpuUsage: cpuUsage,
                responseTime: responseTime
            };
        } catch (error) {
            logger.error('获取系统统计失败:', error);
            return {
                uptime: '0s',
                memoryUsage: 0,
                cpuUsage: 0,
                responseTime: 0
            };
        }
    }

    /**
     * 检查用户是否有审批权限
     */
    canUserApprove(user) {
        if (!user) return false;

        // 管理员不参与审批流程，只有查看和管理权限
        const role = user.role.toLowerCase();
        if (role === 'admin' || user.role === '管理员') {
            return false;
        }

        const approvalRoles = ['厂长', 'factory_manager', '总监', 'director', '经理', 'manager', 'CEO', 'ceo'];
        return approvalRoles.includes(user.role) || (user.permissions && user.permissions.includes('pending_approval'));
    }

    /**
     * 获取待我审批的申请数量
     */
    async getMyPendingApprovalsCount(user) {
        try {
            const pendingApps = await applicationService.getPendingApplications(user);
            // applicationService.getPendingApplications 直接返回数组，不是包装对象
            return Array.isArray(pendingApps) ? pendingApps.length : 0;
        } catch (error) {
            logger.error('获取待审批申请数量失败:', error);
            return 0;
        }
    }

    /**
     * 获取最近活动
     */
    async getRecentActivities(user, limit = 10) {
        try {
            const { databaseAdapter } = require('../database/databaseAdapter');
            const applicationRepository = databaseAdapter.getApplicationRepository();
            const activities = [];

            // 获取最近的申请活动
            try {
                const recentAppsResult = await applicationRepository.query(`
                    SELECT
                        'application' as type,
                        id,
                        applicant as user,
                        CASE
                            WHEN length(content) > 30 THEN substring(content, 1, 30) || '...'
                            ELSE content
                        END as title,
                        created_at as time,
                        status,
                        '提交了申请' as action
                    FROM applications
                    WHERE content IS NOT NULL AND content != ''
                    ORDER BY created_at DESC
                    LIMIT $1
                `, [Math.floor(limit / 2)]);

                const recentApps = recentAppsResult.rows;
                activities.push(...recentApps.map(app => ({
                    ...app,
                    title: `${app.action}: ${app.title}`,
                    description: `申请状态: ${this.getStatusText(app.status)}`
                })));
            } catch (e) {
                logger.warn('获取申请活动失败:', e.message);
            }

            // 获取最近的质量报告活动
            try {
                const qualityRepository = databaseAdapter.getQualityRepository();
                const recentQualityResult = await qualityRepository.query(`
                    SELECT
                        'quality' as type,
                        id,
                        uploaded_by as user,
                        CASE
                            WHEN length(title) > 30 THEN substring(title, 1, 30) || '...'
                            ELSE title
                        END as title,
                        uploaded_at as time,
                        status,
                        '上传了质量报告' as action
                    FROM quality_reports
                    WHERE title IS NOT NULL AND title != ''
                    ORDER BY uploaded_at DESC
                    LIMIT $1
                `, [Math.floor(limit / 2)]);

                const recentQuality = recentQualityResult.rows;
                activities.push(...recentQuality.map(quality => ({
                    ...quality,
                    title: `${quality.action}: ${quality.title}`,
                    description: `报告状态: ${this.getStatusText(quality.status)}`
                })));
            } catch (e) {
                logger.warn('获取质量报告活动失败:', e.message);
            }

            // 不再生成示例活动数据，只返回真实数据

            // 按时间排序并限制数量
            activities.sort((a, b) => new Date(b.time) - new Date(a.time));
            return activities.slice(0, limit);
        } catch (error) {
            logger.error('获取最近活动失败:', error);
            // 返回示例数据作为后备
            return [
                {
                    type: 'system',
                    id: 'fallback-1',
                    user: '系统',
                    title: '系统正常运行',
                    description: '所有服务运行正常',
                    time: new Date().toISOString(),
                    status: 'active'
                }
            ];
        }
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'pending': '待审核',
            'approved': '已通过',
            'rejected': '已拒绝',
            'published': '已发布',
            'draft': '草稿',
            'active': '活跃',
            'completed': '已完成',
            'in_progress': '进行中'
        };
        return statusMap[status] || status || '未知';
    }

    /**
     * 获取通知信息
     */
    async getNotifications(user, limit = 5) {
        try {
            const notifications = [];

            // 获取待审批通知
            if (this.canUserApprove(user)) {
                const pendingCount = await this.getMyPendingApprovalsCount(user);
                if (pendingCount > 0) {
                    notifications.push({
                        type: 'approval',
                        title: '待审批申请',
                        message: `您有 ${pendingCount} 个申请待审批`,
                        time: new Date().toISOString(),
                        priority: 'high'
                    });
                }
            }

            // 获取设备预警通知
            const { databaseAdapter } = require('../database/databaseAdapter');
            const equipmentRepository = databaseAdapter.getEquipmentRepository();
            try {
                const warningQuery = `SELECT COUNT(*) as count FROM equipment_health WHERE health_score < 70`;
                const warningResult = await equipmentRepository.query(warningQuery);
                const warningCount = parseInt(warningResult.rows[0].count) || 0;
                if (warningCount > 0) {
                    notifications.push({
                        type: 'equipment',
                        title: '设备预警',
                        message: `${warningCount} 台设备需要关注`,
                        time: new Date().toISOString(),
                        priority: 'medium'
                    });
                }
            } catch (e) {
                // 忽略健康度表不存在的错误
            }

            // 系统通知
            notifications.push({
                type: 'system',
                title: '系统运行正常',
                message: '所有服务运行正常',
                time: new Date().toISOString(),
                priority: 'low'
            });

            return notifications.slice(0, limit);
        } catch (error) {
            logger.error('获取通知信息失败:', error);
            return [];
        }
    }
}

module.exports = new DashboardService();
