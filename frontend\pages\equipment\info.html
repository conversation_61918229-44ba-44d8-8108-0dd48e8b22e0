<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备信息管理 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/equipment/info.css">

    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 移动端菜单按钮 -->
        <button @click="toggleSidebar" class="md:hidden fixed top-4 left-4 z-20 bg-white p-2 rounded-md shadow-md border border-gray-200">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- 移动端遮罩层 -->
        <div v-if="sidebarOpen" @click="closeSidebar" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-15"></div>

        <!-- 侧边导航栏 -->
        <sidebar :user="currentUser" :sidebar-open="sidebarOpen"></sidebar>

        <div class="flex-1 ml-0 md:ml-72 p-2 md:p-4">
            <div class="bg-white rounded-lg shadow-md p-3 md:p-6">
                <h2 class="text-lg md:text-xl font-semibold text-gray-800 mb-4 md:mb-6">设备信息管理</h2>

                <!-- 操作按钮区域 -->
                <div class="flex flex-col sm:flex-row items-start sm:items-center justify-end mb-4 md:mb-6 space-y-3 sm:space-y-0">
                    <div class="flex flex-wrap items-center gap-2 sm:gap-3 w-full sm:w-auto">
                        <button @click="openFactoryModal" class="bg-gray-600 hover:bg-gray-700 text-white px-3 md:px-4 py-2 rounded-lg flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <span>厂区管理</span>
                        </button>
                        <button @click="showAddModal = true" class="bg-blue-500 hover:bg-blue-600 text-white px-3 md:px-4 py-2 rounded-lg flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span>添加设备</span>
                        </button>
                        <button @click="batchDeleteEquipment"
                                :disabled="selectedEquipment.length === 0"
                                :class="selectedEquipment.length === 0
                                    ? 'bg-gray-400 cursor-not-allowed text-white'
                                    : 'bg-red-500 hover:bg-red-600 text-white'"
                                class="px-3 md:px-4 py-2 rounded-lg flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center transition-colors">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            <span class="hidden sm:inline">批量删除 {{ selectedEquipment.length > 0 ? `(${selectedEquipment.length})` : '' }}</span>
                            <span class="sm:hidden">删除{{ selectedEquipment.length > 0 ? `(${selectedEquipment.length})` : '' }}</span>
                        </button>
                        <button @click="importEquipment" class="bg-green-500 hover:bg-green-600 text-white px-3 md:px-4 py-2 rounded-lg flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                            <span class="hidden sm:inline">导入设备</span>
                            <span class="sm:hidden">导入</span>
                        </button>
                        <button @click="exportEquipment" :disabled="isExporting"
                                :class="isExporting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'"
                                class="text-white px-3 md:px-4 py-2 rounded-lg flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center">
                            <svg v-if="!isExporting" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <svg v-if="isExporting" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span class="hidden sm:inline">{{ isExporting ? '导出中...' : '导出设备' }}</span>
                            <span class="sm:hidden">{{ isExporting ? '导出中' : '导出' }}</span>
                        </button>
                        <button @click="refreshData()" :disabled="refreshing" class="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-3 md:px-4 py-2 rounded-lg flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center transition-colors">
                            <!-- 加载动画图标 -->
                            <svg v-if="refreshing" class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <!-- 普通刷新图标 -->
                            <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <span class="hidden sm:inline">{{ refreshing ? '刷新中...' : '刷新' }}</span>
                        </button>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 mb-4 md:mb-6">
                    <!-- 设备总数 -->
                    <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-xl md:text-2xl font-bold">{{ statistics.totalEquipment }}</div>
                                <div class="text-xs md:text-sm opacity-90 mt-1">设备总数</div>
                            </div>
                            <div class="text-2xl md:text-3xl opacity-20">
                                <svg fill="currentColor" viewBox="0 0 24 24" class="w-6 h-6 md:w-8 md:h-8">
                                    <path d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- 启用设备 -->
                    <div class="stat-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-xl md:text-2xl font-bold">{{ statistics.runningEquipment }}</div>
                                <div class="text-xs md:text-sm opacity-90 mt-1">启用设备</div>
                                <div class="text-xs opacity-75 mt-1">{{ statistics.runningPercentage }}%</div>
                            </div>
                            <div class="text-2xl md:text-3xl opacity-20">
                                <svg fill="currentColor" viewBox="0 0 24 24" class="w-6 h-6 md:w-8 md:h-8">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- 停用设备 -->
                    <div class="stat-card" style="background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-xl md:text-2xl font-bold">{{ statistics.faultEquipment }}</div>
                                <div class="text-xs md:text-sm opacity-90 mt-1">停用设备</div>
                                <div class="text-xs opacity-75 mt-1">{{ statistics.faultPercentage }}%</div>
                            </div>
                            <div class="text-2xl md:text-3xl opacity-20">
                                <svg fill="currentColor" viewBox="0 0 24 24" class="w-6 h-6 md:w-8 md:h-8">
                                    <path d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- 平均使用时间 -->
                    <div class="stat-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-xl md:text-2xl font-bold">{{ statistics.averageUsage }}</div>
                                <div class="text-xs md:text-sm opacity-70 mt-1">平均使用时间</div>
                                <div class="text-xs opacity-60 mt-1">年</div>
                            </div>
                            <div class="text-2xl md:text-3xl opacity-20">
                                <svg fill="currentColor" viewBox="0 0 24 24" class="w-6 h-6 md:w-8 md:h-8">
                                    <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 mb-6">
                    <!-- 设备状态分布图 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">设备状态分布</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                <div class="flex items-center space-x-1">
                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span>启用</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                    <span>停用</span>
                                </div>
                            </div>
                        </div>
                        <div class="relative h-48">
                            <canvas id="statusChart" class="w-full h-full"></canvas>
                        </div>
                    </div>

                    <!-- 厂区设备分布图 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">厂区设备分布</h3>
                            <button @click="refreshCharts" class="text-blue-600 hover:text-blue-800 text-sm">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                刷新
                            </button>
                        </div>
                        <div class="relative h-48">
                            <canvas id="factoryChart" class="w-full h-full"></canvas>
                        </div>
                    </div>
                </div>

                        <!-- 设备列表 -->
                        <div class="chart-container">
                            <!-- 筛选器 -->
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-3 md:gap-4 mb-4 md:mb-6">
                                <div class="flex flex-col space-y-1">
                                    <label class="text-xs md:text-sm text-gray-600">搜索设备:</label>
                                    <input v-model="searchKeyword" type="text" placeholder="设备编号、名称等"
                                           class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div class="flex flex-col space-y-1">
                                    <label class="text-xs md:text-sm text-gray-600">厂区:</label>
                                    <select v-model="selectedArea" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">全部厂区</option>
                                        <option v-for="factory in factoryBaseList" :key="factory.id" :value="factory.name">
                                            {{ factory.id }} - {{ factory.name }}
                                        </option>
                                    </select>
                                </div>
                                <div class="flex flex-col space-y-1">
                                    <label class="text-xs md:text-sm text-gray-600">设备状态:</label>
                                    <select v-model="selectedStatus" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">全部状态</option>
                                        <option value="active">启用</option>
                                        <option value="inactive">停用</option>
                                    </select>
                                </div>
                                <div class="flex flex-col space-y-1">
                                    <label class="text-xs md:text-sm text-gray-600">设备位置:</label>
                                    <select v-model="selectedLocation" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">全部位置</option>
                                        <option v-for="location in filterOptions.locations" :key="location.location" :value="location.location">
                                            {{ location.location }} ({{ location.count }}台设备)
                                        </option>
                                    </select>
                                </div>
                                <div class="flex flex-col space-y-1">
                                    <label class="text-xs md:text-sm text-gray-600">负责人:</label>
                                    <select v-model="selectedResponsible" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">全部负责人</option>
                                        <option v-for="responsible in filterOptions.responsibles" :key="responsible.responsible" :value="responsible.responsible">
                                            {{ responsible.responsible }} ({{ responsible.count }}台设备)
                                        </option>
                                    </select>
                                </div>
                            </div>

                            <!-- 设备表格 - 桌面端 -->
                            <div class="hidden md:block overflow-x-auto">
                                <table class="w-full">
                                    <thead>
                                        <tr class="border-b border-gray-200">
                                            <th class="text-left py-3 px-4">
                                                <input type="checkbox" v-model="selectAll" @change="toggleSelectAll"
                                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            </th>
                                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50 select-none" @click="handleSort('code')">
                                                设备编号
                                                <svg :class="getSortIconClass('code')" class="w-4 h-4 inline ml-1 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getSortIconPath('code')"></path>
                                                </svg>
                                            </th>
                                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50 select-none" @click="handleSort('name')">
                                                设备名称
                                                <svg :class="getSortIconClass('name')" class="w-4 h-4 inline ml-1 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getSortIconPath('name')"></path>
                                                </svg>
                                            </th>
                                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50 select-none" @click="handleSort('area')">
                                                厂区
                                                <svg :class="getSortIconClass('area')" class="w-4 h-4 inline ml-1 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getSortIconPath('area')"></path>
                                                </svg>
                                            </th>
                                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">设备位置</th>
                                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">负责人</th>
                                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50 select-none" @click="handleSort('manufacture_date')">
                                                进厂日期
                                                <svg :class="getSortIconClass('manufacture_date')" class="w-4 h-4 inline ml-1 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getSortIconPath('manufacture_date')"></path>
                                                </svg>
                                            </th>
                                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50 select-none" @click="handleSort('status')">
                                                状态
                                                <svg :class="getSortIconClass('status')" class="w-4 h-4 inline ml-1 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getSortIconPath('status')"></path>
                                                </svg>
                                            </th>
                                            <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="equipment in filteredEquipment" :key="equipment.id"
                                            class="border-b border-gray-100 hover:bg-gray-50">
                                            <td class="py-3 px-4">
                                                <input type="checkbox" v-model="selectedEquipment" :value="equipment.id"
                                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-900">{{ equipment.code }}</td>
                                            <td class="py-3 px-4 text-sm text-gray-900">{{ equipment.name }}</td>
                                            <td class="py-3 px-4 text-sm text-gray-600">{{ getFactoryId(equipment.area) }}</td>
                                            <td class="py-3 px-4 text-sm text-gray-600">{{ equipment.location }}</td>
                                            <td class="py-3 px-4 text-sm text-gray-600">{{ equipment.responsible }}</td>
                                            <td class="py-3 px-4 text-sm text-gray-600">{{ formatDate(equipment.manufacture_date) }}</td>
                                            <td class="py-3 px-4">
                                                <span :class="getStatusClass(equipment.status)">{{ getStatusText(equipment.status) }}</span>
                                            </td>
                                            <td class="py-3 px-4">
                                                <div class="flex items-center space-x-2">
                                                    <button @click="pageInitialized && viewEquipment(equipment)" class="action-btn btn-view">查看</button>
                                                    <button @click="editEquipment(equipment)" class="action-btn btn-edit">编辑</button>
                                                    <button @click="deleteEquipment(equipment)" class="action-btn btn-delete">删除</button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 设备卡片 - 移动端 -->
                            <div class="md:hidden space-y-3">
                                <div v-for="equipment in filteredEquipment" :key="equipment.id"
                                     class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                                    <!-- 卡片头部 -->
                                    <div class="flex items-start justify-between mb-3">
                                        <div class="flex items-center space-x-3">
                                            <input type="checkbox" :value="equipment.id" v-model="selectedEquipment"
                                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <div>
                                                <h3 class="font-medium text-gray-900 text-sm">{{ equipment.name }}</h3>
                                                <p class="text-xs text-gray-500">{{ equipment.code }}</p>
                                            </div>
                                        </div>
                                        <span :class="getStatusClass(equipment.status)" class="text-xs">
                                            {{ getStatusText(equipment.status) }}
                                        </span>
                                    </div>

                                    <!-- 卡片内容 -->
                                    <div class="grid grid-cols-2 gap-3 text-xs">
                                        <div>
                                            <span class="text-gray-500">厂区:</span>
                                            <span class="text-gray-900 ml-1">{{ getFactoryId(equipment.area) }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">位置:</span>
                                            <span class="text-gray-900 ml-1">{{ equipment.location }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">负责人:</span>
                                            <span class="text-gray-900 ml-1">{{ equipment.responsible }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">进厂日期:</span>
                                            <span class="text-gray-900 ml-1">{{ formatDate(equipment.manufacture_date) }}</span>
                                        </div>
                                    </div>

                                    <!-- 卡片操作按钮 -->
                                    <div class="flex items-center justify-end space-x-2 mt-3 pt-3 border-t border-gray-100">
                                        <button @click="pageInitialized && viewEquipment(equipment)"
                                                class="text-blue-600 hover:text-blue-800 text-xs px-2 py-1 rounded border border-blue-200 hover:bg-blue-50">
                                            查看
                                        </button>
                                        <button @click="editEquipment(equipment)"
                                                class="text-green-600 hover:text-green-800 text-xs px-2 py-1 rounded border border-green-200 hover:bg-green-50">
                                            编辑
                                        </button>
                                        <button @click="deleteEquipment(equipment)"
                                                class="text-red-600 hover:text-red-800 text-xs px-2 py-1 rounded border border-red-200 hover:bg-red-50">
                                            删除
                                        </button>
                                    </div>
                                </div>

                                <!-- 空状态 -->
                                <div v-if="filteredEquipment.length === 0" class="text-center py-8">
                                    <svg class="w-12 h-12 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                    </svg>
                                    <p class="text-gray-500 text-sm">暂无设备数据</p>
                                </div>
                            </div>

                            <!-- 分页组件 -->
                            <div v-if="totalEquipment > 0" class="flex items-center justify-between mt-6">
                                <!-- 左侧：记录统计信息 -->
                                <div class="text-sm text-gray-600">
                                    共 {{ totalEquipment }} 条记录，每页 {{ itemsPerPage }} 条
                                </div>

                                <!-- 右侧：分页控件 -->
                                <div class="flex items-center space-x-2">
                                    <!-- 上一页 -->
                                    <button @click="goToPage(currentPage - 1)"
                                            :disabled="currentPage <= 1"
                                            :class="['px-3 py-1 rounded-md border', currentPage <= 1 ? 'border-gray-200 text-gray-400 cursor-not-allowed' : 'border-gray-300 text-gray-700 hover:bg-gray-50']">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                        </svg>
                                    </button>

                                    <!-- 动态分页按钮 -->
                                    <template v-if="totalPages <= 7">
                                        <button v-for="page in totalPages" :key="page" @click="goToPage(page)"
                                                :class="['px-3 py-1 rounded-md', currentPage === page ? 'bg-blue-500 text-white' : 'border border-gray-300 text-gray-700 hover:bg-gray-50']">
                                            {{ page }}
                                        </button>
                                    </template>
                                    <template v-else>
                                        <!-- 前部分页码 -->
                                        <button v-if="currentPage > 3" @click="goToPage(1)"
                                                class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50">
                                            1
                                        </button>
                                        <span v-if="currentPage > 4" class="px-1 text-gray-500">...</span>

                                        <!-- 中间页码 -->
                                        <button v-for="page in 3" :key="page"
                                                v-if="currentPage - 2 + page > 0 && currentPage - 2 + page <= totalPages"
                                                @click="goToPage(currentPage - 2 + page)"
                                                :class="['px-3 py-1 rounded-md', currentPage === (currentPage - 2 + page) ? 'bg-blue-500 text-white' : 'border border-gray-300 text-gray-700 hover:bg-gray-50']">
                                            {{ currentPage - 2 + page }}
                                        </button>

                                        <!-- 后部分页码 -->
                                        <span v-if="currentPage < totalPages - 3" class="px-1 text-gray-500">...</span>
                                        <button v-if="currentPage < totalPages - 2" @click="goToPage(totalPages)"
                                                class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50">
                                            {{ totalPages }}
                                        </button>
                                    </template>

                                    <!-- 下一页 -->
                                    <button @click="goToPage(currentPage + 1)"
                                            :disabled="currentPage >= totalPages"
                                            :class="['px-3 py-1 rounded-md border', currentPage >= totalPages ? 'border-gray-200 text-gray-400 cursor-not-allowed' : 'border-gray-300 text-gray-700 hover:bg-gray-50']">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </button>

                                    <!-- 每页显示数量选择器 -->
                                    <select v-model="itemsPerPage" @change="handlePageSizeChange"
                                            class="ml-4 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                        <option :value="10">10条/页</option>
                                        <option :value="20">20条/页</option>
                                        <option :value="50">50条/页</option>
                                        <option :value="100">100条/页</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

    <!-- 添加设备模态框 -->
    <div v-if="showAddModal && canShowModals" v-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="showAddModal = false">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-2 md:mx-4 max-h-[90vh] overflow-hidden" @click.stop>
            <!-- 模态框头部 -->
            <div class="px-4 md:px-6 py-3 md:py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-base md:text-lg font-semibold text-gray-900">添加设备</h3>
                <button @click="showAddModal = false" class="text-gray-400 hover:text-gray-600 md:hidden">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- 模态框内容 -->
            <div class="px-4 md:px-6 py-3 md:py-4 space-y-3 md:space-y-4 overflow-y-auto max-h-[calc(90vh-120px)]">
                <!-- 设备编号 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        设备编号 <span class="text-red-500">*</span>
                    </label>
                    <input v-model="newEquipment.code" type="text" placeholder="请输入设备编号"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- 设备名称 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        设备名称 <span class="text-red-500">*</span>
                    </label>
                    <input v-model="newEquipment.name" type="text" placeholder="请输入设备名称"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- 厂区 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        厂区 <span class="text-red-500">*</span>
                    </label>
                    <select v-model="newEquipment.area"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white">
                        <option value="">请选择厂区</option>
                        <option v-for="factory in factoryBaseList" :key="factory.id" :value="factory.name">
                            {{ factory.name }}
                        </option>
                    </select>
                </div>

                <!-- 设备位置 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        设备位置 <span class="text-red-500">*</span>
                    </label>
                    <input v-model="newEquipment.location" type="text" placeholder="请输入设备位置"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- 负责人 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        负责人 <span class="text-red-500">*</span>
                    </label>
                    <input v-model="newEquipment.responsible" type="text" placeholder="请输入负责人"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- 设备进厂日期 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        设备进厂日期 <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <input v-model="newEquipment.manufacture_date" type="date"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <p class="text-xs text-gray-500 mt-1">用于准确计算设备年龄和健康度评估</p>
                </div>

                <!-- 设备状态 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">设备状态</label>
                    <select v-model="newEquipment.status"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white">
                        <option value="启用">启用</option>
                        <option value="停用">停用</option>
                    </select>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="px-4 md:px-6 py-3 md:py-4 border-t border-gray-200 flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3">
                <button @click="showAddModal = false"
                        class="w-full sm:w-auto px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    取消
                </button>
                <button @click="addEquipment"
                        class="w-full sm:w-auto px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    添加设备
                </button>
            </div>
        </div>
    </div>

    <!-- 编辑设备模态框 -->
    <div v-if="showEditModal && canShowModals" v-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="showEditModal = false">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto" @click.stop>
            <!-- 模态框头部 -->
            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">编辑设备</h3>
                <button @click="showEditModal = false" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- 模态框内容 -->
            <div class="px-6 py-4 space-y-4">
                <!-- 设备编号 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        设备编号 <span class="text-red-500">*</span>
                    </label>
                    <input v-model="currentEquipment.code" type="text" placeholder="请输入设备编号"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- 设备名称 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        设备名称 <span class="text-red-500">*</span>
                    </label>
                    <input v-model="currentEquipment.name" type="text" placeholder="请输入设备名称"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- 厂区 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        厂区 <span class="text-red-500">*</span>
                    </label>
                    <select v-model="currentEquipment.area"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white">
                        <option value="">请选择厂区</option>
                        <option v-for="factory in factoryBaseList" :key="factory.id" :value="factory.name">
                            {{ factory.name }}
                        </option>
                    </select>
                </div>

                <!-- 设备位置 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        设备位置 <span class="text-red-500">*</span>
                    </label>
                    <input v-model="currentEquipment.location" type="text" placeholder="请输入设备位置"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- 负责人 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        负责人 <span class="text-red-500">*</span>
                    </label>
                    <input v-model="currentEquipment.responsible" type="text" placeholder="请输入负责人"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- 进厂日期 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        进厂日期 <span class="text-red-500">*</span>
                    </label>
                    <input v-model="currentEquipment.manufacture_date" type="date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- 设备状态 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">设备状态</label>
                    <select v-model="currentEquipment.status"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white">
                        <option value="启用">启用</option>
                        <option value="停用">停用</option>
                    </select>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button @click="showEditModal = false"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    取消
                </button>
                <button @click="updateEquipment"
                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    保存修改
                </button>
            </div>
        </div>
    </div>

    <!-- 厂区管理模态框 -->
    <div v-if="showAddFactoryModal && canShowModals" v-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" @click.self="closeFactoryModal">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-hidden" @click.stop>
            <!-- 模态框头部 -->
            <div class="px-4 md:px-6 py-3 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">厂区管理</h3>
                <button @click="closeFactoryModal" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- 模态框内容 -->
            <div class="flex flex-col lg:flex-row max-h-[calc(90vh-80px)] overflow-hidden">
                <!-- 加载状态 -->
                <div v-if="modalOperationInProgress" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                    <div class="flex items-center space-x-2">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                        <span class="text-gray-600">加载中...</span>
                    </div>
                </div>

                <!-- 左侧：厂区列表 -->
                <div class="w-full lg:w-2/3 lg:border-r border-gray-200 p-3 md:p-6">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 space-y-2 sm:space-y-0">
                        <h4 class="text-sm md:text-base font-medium text-gray-800">厂区列表</h4>
                        <button @click="showAddForm = true" class="w-full sm:w-auto bg-blue-500 hover:bg-blue-600 text-white px-3 md:px-4 py-2 rounded-lg flex items-center justify-center space-x-2 text-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span>添加厂区</span>
                        </button>
                    </div>

                    <!-- 厂区表格 - 桌面端 -->
                    <div class="hidden md:block overflow-y-auto max-h-[500px]">
                        <table class="w-full">
                            <thead class="bg-gray-50 sticky top-0">
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">厂区ID</th>
                                    <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">厂区名称</th>
                                    <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">描述</th>
                                    <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">设备数量</th>
                                    <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="factory in factoryModalListWithCount" :key="factory.id" class="border-b border-gray-100 hover:bg-gray-50">
                                    <td class="py-3 px-4 text-sm text-gray-900">{{ factory.id }}</td>
                                    <td class="py-3 px-4 text-sm text-gray-900">{{ factory.name }}</td>
                                    <td class="py-3 px-4 text-sm text-gray-600">{{ factory.description || '-' }}</td>
                                    <td class="py-3 px-4 text-sm">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ factory.equipmentCount }} 台设备
                                        </span>
                                    </td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center space-x-2">
                                            <button @click="editFactory(factory)" class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button @click="deleteFactory(factory)" class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 厂区卡片 - 移动端 -->
                    <div class="md:hidden space-y-3 max-h-[500px] overflow-y-auto">
                        <div v-for="factory in factoryModalListWithCount" :key="factory.id"
                             class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                            <!-- 卡片头部 -->
                            <div class="flex items-start justify-between mb-3">
                                <div>
                                    <h3 class="font-medium text-gray-900 text-sm">{{ factory.name }}</h3>
                                    <p class="text-xs text-gray-500">ID: {{ factory.id }}</p>
                                </div>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ factory.equipmentCount }} 台设备
                                </span>
                            </div>

                            <!-- 卡片内容 -->
                            <div class="mb-3">
                                <p class="text-xs text-gray-600">{{ factory.description || '暂无描述' }}</p>
                            </div>

                            <!-- 卡片操作按钮 -->
                            <div class="flex items-center justify-end space-x-2 pt-3 border-t border-gray-100">
                                <button @click="editFactory(factory)"
                                        class="text-blue-600 hover:text-blue-800 text-xs px-2 py-1 rounded border border-blue-200 hover:bg-blue-50">
                                    编辑
                                </button>
                                <button @click="deleteFactory(factory)"
                                        class="text-red-600 hover:text-red-800 text-xs px-2 py-1 rounded border border-red-200 hover:bg-red-50">
                                    删除
                                </button>
                            </div>
                        </div>

                        <!-- 空状态 -->
                        <div v-if="factoryModalListWithCount.length === 0" class="text-center py-8">
                            <svg class="w-12 h-12 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                            </svg>
                            <p class="text-gray-500 text-sm">暂无厂区数据</p>
                        </div>
                    </div>
                </div>

                <!-- 右侧：添加厂区表单 -->
                <div class="w-full lg:w-1/3 p-3 md:p-6 border-t lg:border-t-0 lg:border-l border-gray-200" v-show="showAddForm">
                    <h4 class="text-base font-medium text-gray-800 mb-4">添加厂区</h4>

                    <div class="space-y-4">
                        <!-- 厂区ID -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                厂区ID <span class="text-red-500">*</span>
                            </label>
                            <input v-model="newFactory.id" type="text" placeholder="请输入厂区ID (如：SR、TW、VN)"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <p class="text-xs text-gray-500 mt-1">厂区ID用于标识厂区，建议使用简短的英文字母</p>
                        </div>

                        <!-- 厂区名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                厂区名称 <span class="text-red-500">*</span>
                            </label>
                            <input v-model="newFactory.name" type="text" placeholder="请输入厂区名称"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- 描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                            <textarea v-model="newFactory.description" placeholder="请输入厂区描述 (可选)" rows="4"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                        </div>

                        <!-- 按钮 -->
                        <div class="flex justify-end space-x-3 pt-4">
                            <button @click="cancelAddFactory"
                                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                                取消
                            </button>
                            <button @click="addFactory"
                                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                保存厂区
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 右侧：空状态（当未显示添加表单时） -->
                <div class="hidden lg:flex w-full lg:w-1/3 p-3 md:p-6 items-center justify-center" v-show="!showAddForm">
                    <div class="text-center text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <p class="text-sm">点击"添加厂区"按钮开始添加新厂区</p>
                    </div>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="px-4 md:px-6 py-3 md:py-4 border-t border-gray-200 flex justify-end">
                <button @click="closeFactoryModal"
                        class="w-full sm:w-auto px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 编辑厂区模态框 -->
    <div v-if="showEditFactoryModal && canShowModals" v-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="showEditFactoryModal = false">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto" @click.stop>
            <!-- 模态框头部 -->
            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">编辑厂区</h3>
                <button @click="showEditFactoryModal = false" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- 模态框内容 -->
            <div class="px-6 py-4 space-y-4">
                <!-- 厂区ID -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        厂区ID <span class="text-red-500">*</span>
                    </label>
                    <input v-model="editingFactory.id" type="text" disabled
                           class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500 cursor-not-allowed">
                    <p class="text-xs text-gray-500 mt-1">厂区ID不可修改</p>
                </div>

                <!-- 厂区名称 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        厂区名称 <span class="text-red-500">*</span>
                    </label>
                    <input v-model="editingFactory.name" type="text" placeholder="请输入厂区名称"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- 描述 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                    <textarea v-model="editingFactory.description" placeholder="请输入厂区描述 (可选)" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button @click="showEditFactoryModal = false"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    取消
                </button>
                <button @click="updateFactory"
                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    保存修改
                </button>
            </div>
        </div>
    </div>

    <!-- 查看设备模态框 -->
    <div v-if="showViewModal && canShowModals && pageInitialized && currentEquipment && currentEquipment.id" v-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="showViewModal = false">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4" @click.stop>
            <!-- 模态框头部 -->
            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">设备详情</h3>
                <button @click="showViewModal = false" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- 模态框内容 -->
            <div class="px-6 py-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">设备编号</label>
                        <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ currentEquipment.code }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">设备名称</label>
                        <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ currentEquipment.name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">所属厂区</label>
                        <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ currentEquipment.area }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">设备位置</label>
                        <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ currentEquipment.location }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">负责人</label>
                        <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ currentEquipment.responsible }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">进厂日期</label>
                        <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ formatDate(currentEquipment.manufacture_date) }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">设备状态</label>
                        <p class="text-sm bg-gray-50 px-3 py-2 rounded-md">
                            <span :class="getStatusClass(currentEquipment.status)">{{ getStatusText(currentEquipment.status) }}</span>
                        </p>
                    </div>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end">
                <button @click="showViewModal = false"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    关闭
                </button>
            </div>
        </div>

        <!-- 隐藏的文件输入元素用于导入 -->
        <input type="file" ref="fileInput" @change="handleFileSelect" accept=".xlsx,.xls" style="display: none;">
    </div>

    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/chart.umd.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/equipment/info.js"></script>
</body>
</html>
