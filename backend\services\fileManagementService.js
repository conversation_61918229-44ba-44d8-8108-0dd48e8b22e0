/**
 * 文件管理服务层
 * 处理文件管理相关的业务逻辑
 */

const { databaseAdapter } = require('../database/databaseAdapter');
const logger = require('../utils/logger');
const path = require('path');
const fs = require('fs');

class FileManagementService {
    constructor() {
        this.repository = databaseAdapter.getFileManagementRepository();
    }

    /**
     * 生成文件编号
     * 格式：FM + YYYYMMDD + 4位序号
     * 编号严格递增，不重复使用已删除的编号
     */
    generateFileNumber() {
        const today = new Date();
        const dateStr = today.getFullYear().toString() +
                       (today.getMonth() + 1).toString().padStart(2, '0') +
                       today.getDate().toString().padStart(2, '0');

        // 获取当天已使用的最大序号（包括已删除的记录）
        const maxSequence = this.repository.getMaxUsedSequenceForDate(`FM${dateStr}`);
        const nextSequence = (maxSequence + 1).toString().padStart(4, '0');

        return `FM${dateStr}${nextSequence}`;
    }

    /**
     * 获取所有客户
     */
    async getAllCustomers() {
        try {
            return this.repository.getAllCustomers();
        } catch (error) {
            logger.error('获取客户列表失败:', error);
            throw new Error('获取客户列表失败');
        }
    }

    /**
     * 获取活跃客户（用于文件上传选择）
     */
    async getActiveCustomers() {
        try {
            return this.repository.getActiveCustomers();
        } catch (error) {
            logger.error('获取活跃客户列表失败:', error);
            throw new Error('获取活跃客户列表失败');
        }
    }

    /**
     * 根据ID获取客户信息
     */
    async getCustomerById(customerId) {
        try {
            return this.repository.getCustomerById(customerId);
        } catch (error) {
            logger.error('获取客户信息失败:', error);
            throw new Error('获取客户信息失败');
        }
    }

    /**
     * 创建客户
     */
    async createCustomer(customerData, userId) {
        try {
            // 检查客户名称是否已存在（包括停用的客户）
            const existingCustomer = this.repository.getCustomerByNameAll(customerData.customer_name);
            if (existingCustomer) {
                throw new Error('客户名称已存在');
            }

            const customer = this.repository.createCustomer({
                ...customerData,
                created_by: userId
            });

            logger.info(`创建客户成功: ${customer.customer_name}`, {
                customerId: customer.id,
                userId
            });

            return customer;
        } catch (error) {
            logger.error('创建客户失败:', error);
            throw error;
        }
    }

    /**
     * 更新客户信息
     */
    async updateCustomer(customerId, customerData, userId) {
        try {
            // 检查客户是否存在
            const existingCustomer = this.repository.getCustomerById(customerId);
            if (!existingCustomer) {
                throw new Error('客户不存在');
            }

            // 如果修改了客户名称，检查新名称是否已被其他客户使用
            if (customerData.customer_name && customerData.customer_name !== existingCustomer.customer_name) {
                const duplicateCustomer = this.repository.getCustomerByNameAll(customerData.customer_name);
                if (duplicateCustomer && duplicateCustomer.id !== customerId) {
                    throw new Error('客户名称已存在');
                }
            }

            const customer = this.repository.updateCustomer(customerId, {
                ...customerData,
                updated_by: userId
            });

            logger.info(`更新客户成功: ${customer.customer_name}`, {
                customerId: customer.id,
                userId
            });

            return customer;
        } catch (error) {
            logger.error('更新客户失败:', error);
            throw error;
        }
    }

    /**
     * 切换客户状态
     */
    async toggleCustomerStatus(customerId, userId) {
        try {
            // 检查客户是否存在
            const existingCustomer = this.repository.getCustomerById(customerId);
            if (!existingCustomer) {
                throw new Error('客户不存在');
            }

            const customer = this.repository.updateCustomer(customerId, {
                active: !existingCustomer.active,
                updated_by: userId
            });

            logger.info(`切换客户状态成功: ${customer.customer_name} -> ${customer.active ? '启用' : '停用'}`, {
                customerId: customer.id,
                userId
            });

            return customer;
        } catch (error) {
            logger.error('切换客户状态失败:', error);
            throw error;
        }
    }

    /**
     * 删除客户
     */
    async deleteCustomer(customerId, userId) {
        try {
            // 检查客户是否存在
            const existingCustomer = this.repository.getCustomerById(customerId);
            if (!existingCustomer) {
                throw new Error('客户不存在');
            }

            // 检查客户是否有关联的文件记录
            const relatedFiles = this.repository.getFilesByCustomerId(customerId);
            if (relatedFiles && relatedFiles.length > 0) {
                throw new Error(`无法删除客户，该客户下还有 ${relatedFiles.length} 个文件记录`);
            }

            // 执行删除操作
            this.repository.deleteCustomer(customerId);

            logger.info(`删除客户成功: ${existingCustomer.customer_name}`, {
                customerId,
                customerName: existingCustomer.customer_name,
                userId
            });

            return { success: true, message: '客户删除成功' };
        } catch (error) {
            logger.error('删除客户失败:', error);
            throw error;
        }
    }

    /**
     * 获取历史批次号
     */
    async getHistoricalBatches(customerId, productModel) {
        try {
            // 检查客户是否存在
            const existingCustomer = this.repository.getCustomerById(customerId);
            if (!existingCustomer) {
                throw new Error('客户不存在');
            }

            // 获取该客户和产品型号的历史批次号
            const batches = this.repository.getHistoricalBatchesByCustomerAndProduct(customerId, productModel);

            // 提取批次号并去重
            const uniqueBatches = [...new Set(batches.map(batch => batch.batch_number))];

            // 按版本号排序
            uniqueBatches.sort((a, b) => {
                const aMatch = a.match(/V(\d+)\.(\d+)/);
                const bMatch = b.match(/V(\d+)\.(\d+)/);

                if (aMatch && bMatch) {
                    const aVersion = parseInt(aMatch[1]) * 100 + parseInt(aMatch[2]);
                    const bVersion = parseInt(bMatch[1]) * 100 + parseInt(bMatch[2]);
                    return aVersion - bVersion;
                }

                return a.localeCompare(b);
            });

            logger.info(`获取历史批次成功: 客户${existingCustomer.customer_name}, 产品${productModel}, 批次数量${uniqueBatches.length}`, {
                customerId,
                productModel,
                batchCount: uniqueBatches.length
            });

            return uniqueBatches;
        } catch (error) {
            logger.error('获取历史批次失败:', error);
            throw error;
        }
    }

    /**
     * 获取客户的产品列表
     */
    async getCustomerProducts(customerId) {
        try {
            return this.repository.getProductsByCustomer(customerId);
        } catch (error) {
            logger.error('获取客户产品列表失败:', error);
            throw new Error('获取客户产品列表失败');
        }
    }

    /**
     * 创建或获取产品
     */
    async createOrGetProduct(productData, userId) {
        try {
            // 先查找是否已存在相同的产品
            let product = this.repository.findProductByCustomerAndModel(
                productData.customer_id,
                productData.product_model,
                productData.batch_number || ''
            );

            if (!product) {
                // 不存在则创建新产品
                product = this.repository.createProduct({
                    ...productData,
                    created_by: userId
                });

                logger.info(`创建产品成功: ${product.product_model}`, {
                    productId: product.id,
                    customerId: product.customer_id,
                    userId
                });
            }

            return product;
        } catch (error) {
            logger.error('创建或获取产品失败:', error);
            throw error;
        }
    }

    /**
     * 获取所有文件记录
     */
    async getAllFiles() {
        try {
            const files = this.repository.getAllFiles();
            
            // 为每个文件记录获取附件信息
            for (const file of files) {
                file.attachments = this.repository.getAttachmentsByFileRecord(file.id);
            }

            return files;
        } catch (error) {
            logger.error('获取文件列表失败:', error);
            throw new Error('获取文件列表失败');
        }
    }

    /**
     * 根据ID获取文件记录详情
     */
    async getFileById(fileId) {
        try {
            const file = this.repository.getFileById(fileId);
            if (!file) {
                throw new Error('文件记录不存在');
            }

            // 获取附件信息
            file.attachments = this.repository.getAttachmentsByFileRecord(fileId);

            return file;
        } catch (error) {
            logger.error('获取文件详情失败:', error);
            throw error;
        }
    }

    /**
     * 创建文件记录
     */
    async createFileRecord(fileData, attachments, userId) {
        try {
            // 生成文件编号
            const fileNumber = this.generateFileNumber();

            // 创建或获取产品
            const product = await this.createOrGetProduct({
                customer_id: fileData.customer_id,
                product_model: fileData.product_model,
                batch_number: fileData.batch_number,
                description: fileData.certification_content
            }, userId);

            // 检查是否为首次版本
            const existingFiles = this.repository.getFilesByProduct(product.id);
            const isFirstVersion = existingFiles.length === 0;
            const version = isFirstVersion ? 1 : this.repository.getMaxVersionForProduct(product.id) + 1;

            // 创建文件记录
            const fileRecord = this.repository.createFileRecord({
                file_number: fileNumber,
                customer_id: fileData.customer_id,
                product_id: product.id,
                title: fileData.title,
                description: fileData.description,
                version: version,
                is_first_version: isFirstVersion,
                change_description: fileData.change_description,
                uploaded_by: userId,
                uploaded_at: new Date().toISOString()
            });

            // 保存附件信息
            if (attachments && attachments.length > 0) {
                for (const attachment of attachments) {
                    this.repository.createAttachment({
                        file_record_id: fileRecord.id,
                        original_filename: attachment.originalname,
                        stored_filename: attachment.filename,
                        file_path: attachment.path,
                        file_size: attachment.size,
                        file_type: path.extname(attachment.originalname),
                        mime_type: attachment.mimetype
                    });
                }
            }

            logger.info(`创建文件记录成功: ${fileRecord.file_number}`, {
                fileId: fileRecord.id,
                version: version,
                isFirstVersion,
                userId
            });

            return fileRecord;
        } catch (error) {
            logger.error('创建文件记录失败:', error);
            throw error;
        }
    }

    /**
     * 创建通知记录
     */
    async createNotifications(fileRecordId, userIds, notificationType = 'new_file') {
        try {
            const notifications = [];
            
            for (const userId of userIds) {
                try {
                    const notification = this.repository.createNotification({
                        file_record_id: fileRecordId,
                        notified_user_id: userId,
                        notification_type: notificationType
                    });
                    notifications.push(notification);
                } catch (error) {
                    logger.error(`创建通知失败 (用户: ${userId}):`, error);
                    // 继续处理其他用户，不中断整个流程
                }
            }

            logger.info(`创建通知记录成功: ${notifications.length}个`, {
                fileRecordId,
                userCount: userIds.length
            });

            return notifications;
        } catch (error) {
            logger.error('创建通知记录失败:', error);
            throw error;
        }
    }

    /**
     * 获取用户的通知列表
     */
    async getUserNotifications(userId) {
        try {
            return this.repository.getNotificationsByUser(userId);
        } catch (error) {
            logger.error('获取用户通知失败:', error);
            throw new Error('获取用户通知失败');
        }
    }

    /**
     * 确认通知
     */
    async confirmNotification(notificationId, userId) {
        try {
            // 首先获取通知信息以获取文件记录ID
            const notification = this.repository.getNotificationsByUser(userId)
                .find(n => n.id === notificationId);

            if (!notification) {
                throw new Error('通知不存在或无权限访问');
            }

            const success = this.repository.confirmNotification(notificationId);

            if (success) {
                logger.info(`确认通知成功: ${notificationId}`, { userId });

                // 检查该文件的所有通知是否都已确认
                const confirmStatus = this.repository.checkAllNotificationsConfirmed(notification.file_record_id);

                if (confirmStatus.allConfirmed) {
                    // 所有用户都已确认，发送完成通知给提交者
                    await this.sendAllConfirmedNotification(notification.file_record_id);
                    logger.info(`所有用户已确认文件通知，已发送完成通知`, {
                        fileRecordId: notification.file_record_id,
                        totalNotifications: confirmStatus.totalNotifications
                    });
                }
            } else {
                throw new Error('通知不存在或已确认');
            }

            return success;
        } catch (error) {
            logger.error('确认通知失败:', error);
            throw error;
        }
    }

    /**
     * 发送所有用户已确认的通知给提交者
     */
    async sendAllConfirmedNotification(fileRecordId) {
        try {
            const uploaderInfo = this.repository.getFileUploader(fileRecordId);

            if (!uploaderInfo || !uploaderInfo.email) {
                logger.warn('无法发送确认完成通知：上传者邮箱不存在', {
                    fileRecordId,
                    uploaderId: uploaderInfo?.uploaded_by
                });
                return;
            }

            // 动态导入emailService以避免循环依赖
            const emailService = require('./emailService');
            await emailService.sendAllConfirmedNotification(uploaderInfo);

        } catch (error) {
            logger.error('发送确认完成通知失败:', error);
            // 不抛出错误，避免影响确认流程
        }
    }

    /**
     * 删除文件记录
     */
    async deleteFileRecord(fileId, userId) {
        try {
            // 获取文件记录
            const file = this.repository.getFileById(fileId);
            if (!file) {
                throw new Error('文件记录不存在');
            }

            // 获取附件列表
            const attachments = this.repository.getAttachmentsByFileRecord(fileId);

            // 删除物理文件
            for (const attachment of attachments) {
                try {
                    if (fs.existsSync(attachment.file_path)) {
                        fs.unlinkSync(attachment.file_path);
                    }
                } catch (error) {
                    logger.error(`删除物理文件失败: ${attachment.file_path}`, error);
                    // 继续处理，不中断删除流程
                }
            }

            // 更新文件记录状态为已删除
            // 注意：这里需要在repository中添加相应的方法
            // this.repository.markFileAsDeleted(fileId);

            logger.info(`删除文件记录成功: ${file.file_number}`, {
                fileId,
                userId
            });

            return true;
        } catch (error) {
            logger.error('删除文件记录失败:', error);
            throw error;
        }
    }

    /**
     * 搜索文件记录
     */
    async searchFiles(searchParams) {
        try {
            // 这里可以根据搜索参数实现更复杂的搜索逻辑
            // 目前先返回所有文件，后续可以扩展
            return await this.getAllFiles();
        } catch (error) {
            logger.error('搜索文件失败:', error);
            throw new Error('搜索文件失败');
        }
    }

    /**
     * 获取文件统计信息
     */
    async getFileStatistics() {
        try {
            const files = await this.getAllFiles();
            const customers = await this.getAllCustomers();

            const stats = {
                totalFiles: files.length,
                totalCustomers: customers.length,
                filesByCustomer: {},
                filesByMonth: {},
                recentFiles: files.slice(0, 10) // 最近10个文件
            };

            // 按客户统计
            files.forEach(file => {
                const customerName = file.customer_name;
                stats.filesByCustomer[customerName] = (stats.filesByCustomer[customerName] || 0) + 1;
            });

            // 按月份统计
            files.forEach(file => {
                const month = file.uploaded_at.substring(0, 7); // YYYY-MM
                stats.filesByMonth[month] = (stats.filesByMonth[month] || 0) + 1;
            });

            return stats;
        } catch (error) {
            logger.error('获取文件统计信息失败:', error);
            throw new Error('获取文件统计信息失败');
        }
    }
}

module.exports = new FileManagementService();
