-- 修复维修保养表结构
-- 将字段名统一为代码中使用的名称

-- 删除旧表（如果存在）
DROP TABLE IF EXISTS equipment_maintenance;

-- 重新创建表，使用正确的字段名
CREATE TABLE equipment_maintenance (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    type VARCHAR(50) NOT NULL,
    severity_level VARCHAR(50),
    description TEXT,
    maintenance_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    cost DECIMAL(10,2) DEFAULT 0,
    technician VARCHAR(100),
    status VARCHAR(20) DEFAULT 'pending',
    notes TEXT,
    result TEXT,
    reviewer VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_equipment_maintenance_equipment_id ON equipment_maintenance (equipment_id);
CREATE INDEX IF NOT EXISTS idx_equipment_maintenance_type ON equipment_maintenance (type);
CREATE INDEX IF NOT EXISTS idx_equipment_maintenance_status ON equipment_maintenance (status);
CREATE INDEX IF NOT EXISTS idx_equipment_maintenance_date ON equipment_maintenance (maintenance_date);

-- 注意：不插入测试数据，保持表为空状态
-- 系统将使用真实的维修保养记录数据
